{"version": "1.0", "algorithm": "sha256", "created_at": "2025-05-21T17:43:43.302927+00:00", "files": [{"path": "CMakeLists.txt", "size": 963, "hash": "16600ac58dee67d58704c4abd5385d3b83a44d00d1eb637038811f62761d49f8"}, {"path": "LICENSE", "size": 11358, "hash": "cfc7749b96f63bd31c3c42b5c471bf756814053e847c10f3eb003417bc523d30"}, {"path": "CHANGELOG.md", "size": 22307, "hash": "870202da579ce373bc0c7ef89c32444bb5299c7c13a2e26757845304bb5497f4"}, {"path": "idf_component.yml", "size": 369, "hash": "4e54d93581bf7e935e5c5b90a250db77949e89e4a2f2f908da6c900a6c4d08b9"}, {"path": "Kconfig", "size": 393, "hash": "3a7ddfa3f715bbd7122cb1ea816b25b2f947e680cf120f6c5b0b53945fbb8f41"}, {"path": ".cz.yaml", "size": 242, "hash": "7ecef87da5ece65d31cd733568b11b2b18f8f957ad33e6f0445d54dbc5d64c9a"}, {"path": "README.md", "size": 705, "hash": "88edfb76a9debd66989c7b57fb2ad878cd45103d6e8c3074e26d80f26a60e35f"}, {"path": "esp_websocket_client.c", "size": 55624, "hash": "c0a3c2be8e40cd045fc6209f00b4a98bc3dfa4f35927ae7761d30eec639d6589"}, {"path": "test/sdkconfig.ci", "size": 94, "hash": "8da7589f815a526c0e9bc8291cd1e58864041b80bec592a91840a5c8847cffa6"}, {"path": "test/CMakeLists.txt", "size": 316, "hash": "b0303bbbab1af35cbafea32bea857754690868a9fe86d4a4bef4ab02fe828f4a"}, {"path": "test/pytest_websocket.py", "size": 213, "hash": "768db549807074c3e8550a332ddaeca236b3e1db743bb72600756104b8b125f8"}, {"path": "test/sdkconfig.defaults", "size": 68, "hash": "bf225acae456eb714b977921a0e9d29bdce06d7113f5544593758357810adc56"}, {"path": "include/esp_websocket_client.h", "size": 22230, "hash": "8e409f87293d09e75cdbd021ba063c78c0cd79927628b63d4673f8151c98f7dc"}, {"path": "examples/target/sdkconfig.ci.dynamic_buffer", "size": 457, "hash": "715dc470a64dd8d8f68cbf895cd3bee7cdf27f9ce3a013cca6d0069dc77ca3b2"}, {"path": "examples/target/sdkconfig.ci", "size": 412, "hash": "79c94d6a200c9b2838d358ab7443a320373cc65a79062252e27e2d55a4f840dc"}, {"path": "examples/target/CMakeLists.txt", "size": 241, "hash": "b9dff8d756d1762e9d327a167e19333f01953f6c1b8a299271f51d502667e9d3"}, {"path": "examples/target/pytest_websocket.py", "size": 9468, "hash": "b3d1ad18d953223a99ea74e5812c2da96ecdd6aa28e77dd3e71edc1c71a87863"}, {"path": "examples/target/README.md", "size": 5841, "hash": "9973a973a13d1989a02fc0e7d173bdcf7f1afb5c17d034e02aec8c01700e8326"}, {"path": "examples/target/sdkconfig.ci.mutual_auth", "size": 489, "hash": "689d5b61ddb8995e5033c4f57c8f4aa22d33d9ad57edd997c4a779ab8343fa95"}, {"path": "examples/target/sdkconfig.ci.plain_tcp", "size": 478, "hash": "93d862bd5e2cf98fc7e154ee5e5493e1db0c9265de2ec768192288dbcecf0adf"}, {"path": "examples/linux/CMakeLists.txt", "size": 450, "hash": "4e107c17e713fa64b029d280cb09bad19808dcabc81946dfef718b1c0efbecfb"}, {"path": "examples/linux/README.md", "size": 1533, "hash": "e435a3aa19fc44bc6ee3a14d76c4cf229407b415628cbb8803d981eb158d87c0"}, {"path": "examples/linux/sdkconfig.ci.coverage", "size": 195, "hash": "e84d023898ae7caf713498f8a675a1d8066d95a5792ff47fb07fda9daab38c5e"}, {"path": "examples/linux/sdkconfig.ci.linux", "size": 173, "hash": "5413d3c6995d51b511ee9189429a9d40d5689954d627dba021da1afa39053a73"}, {"path": "examples/linux/sdkconfig.defaults", "size": 173, "hash": "5413d3c6995d51b511ee9189429a9d40d5689954d627dba021da1afa39053a73"}, {"path": "examples/linux/main/CMakeLists.txt", "size": 633, "hash": "b0b910d796a1d79992830e81acf49cf7e66f3cd93482123e628c2bae10575496"}, {"path": "examples/linux/main/websocket_linux.c", "size": 5700, "hash": "bdcd05ebbb644b46ba9d389c76057fd13800befc1ffca0597e563a198ccb49e3"}, {"path": "examples/linux/main/Kconfig.projbuild", "size": 387, "hash": "4252986c63574264d005d390fe4cc038aa9bf67e9690d3f85941208f845629e4"}, {"path": "examples/target/main/CMakeLists.txt", "size": 660, "hash": "90fa2a890cfe4e633bacfad2f276024e37a3081541c6cb6f2b20d46f3fe5815f"}, {"path": "examples/target/main/idf_component.yml", "size": 244, "hash": "eb21b3172db34113389d60c0d97bcd6db89fa7b1e4412075c0e3b258c96712d6"}, {"path": "examples/target/main/Kconfig.projbuild", "size": 1724, "hash": "a2f0ee54c5057b98c4a13f1adfeb472b5fa83194fe410151b6a77273b7c3f54d"}, {"path": "examples/target/main/websocket_example.c", "size": 9979, "hash": "fd43ef5b33cfaad14f17fe78780bacc63c6f0e2bd66cfe68403aafd79abac238"}, {"path": "examples/target/main/certs/client_key.pem", "size": 1704, "hash": "0374e438a51a05583d2d1c4995ff8d91f1dbf67a3cfd6b698c9b7f87b9f7396a"}, {"path": "examples/target/main/certs/client_cert.pem", "size": 1224, "hash": "3c03fbdef579ecab88db6b6bb02f83d4336fbf765f66ffbb2fb4e7855a200634"}, {"path": "examples/target/main/certs/ca_cert.pem", "size": 1245, "hash": "3eaa8c88142a67113b0b3826c7487612b08e11a5a661e0e4a598dc9646deaf90"}, {"path": "examples/target/main/certs/server/server_cert.pem", "size": 1224, "hash": "fd2213c3d99986fe45c64c541c47c0ddc4548905f69efd5c8ddc99620bbcf741"}, {"path": "examples/target/main/certs/server/server_key.pem", "size": 1704, "hash": "e889be6cc6d7b4144b6dc87a22b0feaa02eda1da0dea15a23d6e8ea7248b34e2"}, {"path": "test/main/CMakeLists.txt", "size": 212, "hash": "c043a53cad79296eef052d6014f53d2d9b607c6a4e078168c617a24a12495447"}, {"path": "test/main/test_websocket_client.c", "size": 2441, "hash": "68d228db40471adc2f3adf002b4f93a2730cd3302cb4609d70929fe7ec5b1a04"}]}