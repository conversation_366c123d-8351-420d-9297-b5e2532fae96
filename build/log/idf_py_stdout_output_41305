ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x17e8
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0mI (27) boot: ESP-IDF v5.4.1-dirty 2nd stage bootloader[0m
[0mI (27) boot: compile time Jul 15 2025 17:50:08[0m
[0mI (27) boot: Multicore bootloader[0m
[0mI (30) boot: chip revision: v0.2[0m
[0mI (33) boot: efuse block revision: v1.3[0m
[0mI (37) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0mI (46) boot.esp32s3: SPI Flash Size : 4MB[0m
[0mI (51) boot: Enabling RNG early entropy source...[0m
[0mI (56) boot: Partition Table:[0m
[0mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0mI (66) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0mI (74) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0mI (81) boot:  2 factory          factory app      00 00 00010000 00300000[0m
[0mI (88) boot: End of partition table[0m
[0mI (92) esp_image: segment 0: paddr=00010020 vaddr=3c0e0020 size=32bd0h (207824) map[0m
[0mI (137) esp_image: segment 1: paddr=00042bf8 vaddr=3fc9c100 size=04ec8h ( 20168) load[0m
[0mI (141) esp_image: segment 2: paddr=00047ac8 vaddr=40374000 size=08550h ( 34128) load[0m
[0mI (150) esp_image: segment 3: paddr=00050020 vaddr=42000020 size=d6d3ch (879932) map[0m
[0mI (305) esp_image: segment 4: paddr=00126d64 vaddr=4037c550 size=0fac0h ( 64192) load[0m
[0mI (320) esp_image: segment 5: paddr=0013682c vaddr=600fe100 size=0001ch (    28) load[0m
[0mI (329) boot: Loaded app from partition at offset 0x10000[0m
[0mI (329) boot: Disabling RNG early entropy source...[0m
[0mI (341) cpu_start: Multicore app[0m
[0mI (350) cpu_start: Pro cpu start user code[0m
[0mI (350) cpu_start: cpu freq: 160000000 Hz[0m
[0mI (350) app_init: Application information:[0m
[0mI (352) app_init: Project name:     esp32_mcp_hardware[0m
[0mI (358) app_init: App version:      1.0.0[0m
[0mI (362) app_init: Compile time:     Jul 15 2025 17:49:57[0m
[0mI (368) app_init: ELF file SHA256:  77bf85560...[0m
[0mI (373) app_init: ESP-IDF:          v5.4.1-dirty[0m
[0mI (378) efuse_init: Min chip rev:     v0.0[0m
[0mI (383) efuse_init: Max chip rev:     v0.99 [0m
[0mI (387) efuse_init: Chip rev:         v0.2[0m
[0mI (392) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0mI (399) heap_init: At 3FCB1000 len 00038710 (225 KiB): RAM[0m
[0mI (405) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0mI (410) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0mI (416) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0mI (423) spi_flash: detected chip: boya[0m
[0mI (427) spi_flash: flash io: dio[0m
[0mW (430) spi_flash: Detected size(16384k) larger than the size in the binary image header(4096k). Using the size in the binary image header.[0m
[0mI (444) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0mI (450) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0mI (458) main_task: Started on CPU0[0m
[0mI (473) main_task: Calling app_main()[0m
[0mI (474) MAIN: 🚀 小智 ESP32 MCP 硬件系统启动[0m
[0mI (474) MAIN: 📋 系统信息:[0m
[0mI (476) MAIN:    - 芯片型号: esp32s3[0m
[0mI (480) MAIN:    - CPU核心数: 2[0m
[0mI (484) MAIN:    - WiFi/802.11bgn[0m
[0mI (488) MAIN:    - Flash大小: 4MB[0m
[0mI (491) MAIN:    - 剩余内存: 268228 bytes[0m
[0mI (496) MAIN: [0m
[0mI (498) MAIN: 💡 初始化状态LED[0m
[0mI (503) gpio: GPIO[48]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (512) MAIN: 📶 初始化WiFi管理器[0m
[0mI (547) pp: pp rom version: e7ae62f[0m
[0mI (548) net80211: net80211 rom version: e7ae62f[0m
[0;32mI (549) wifi:wifi driver task: 3fcbd420, prio:23, stack:6656, core=0[0m
[0;32mI (566) wifi:wifi firmware version: 79fa3f41ba[0m
[0;32mI (567) wifi:wifi certification version: v7.0[0m
[0;32mI (567) wifi:config NVS flash: enabled[0m
[0;32mI (567) wifi:config nano formatting: disabled[0m
[0;32mI (572) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (576) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (580) wifi:Init management short buffer num: 32[0m
[0;32mI (585) wifi:Init static tx buffer num: 16[0m
[0;32mI (589) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (593) wifi:Init static rx buffer size: 1600[0m
[0;32mI (597) wifi:Init static rx buffer num: 10[0m
[0;32mI (601) wifi:Init dynamic rx buffer num: 32[0m
[0mI (605) wifi_init: rx ba win: 6[0m
[0mI (608) wifi_init: accept mbox: 6[0m
[0mI (612) wifi_init: tcpip mbox: 64[0m
[0mI (615) wifi_init: udp mbox: 32[0m
[0mI (619) wifi_init: tcp mbox: 32[0m
[0mI (623) wifi_init: tcp tx win: 65535[0m
[0mI (627) wifi_init: tcp rx win: 65535[0m
[0mI (631) wifi_init: tcp mss: 1440[0m
[0mI (634) wifi_init: WiFi IRAM OP enabled[0m
[0mI (638) wifi_init: WiFi RX IRAM OP enabled[0m
[0mI (643) WIFI_MANAGER: WiFi manager initialized[0m
[0mI (648) MAIN: 👁️ 初始化双屏眼睛显示系统[0m
[0mI (654) EYE_DISPLAY: 🚀 初始化双屏眼睛显示系统[0m
[0mI (660) EYE_DISPLAY: 🚌 初始化SPI总线[0m
[0mI (665) EYE_DISPLAY: 📚 初始化LVGL库[0m
[0mI (669) EYE_DISPLAY: 💾 为左屏分配显示缓冲区[0m
[0mI (674) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 2)[0m
[0mI (681) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (690) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (699) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (830) gpio: GPIO[47]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (831) EYE_DISPLAY: 💡 背光已打开 (引脚: 47)[0m
[0mI (834) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 2)[0m
[0mI (845) EYE_DISPLAY: 💾 为右屏分配显示缓冲区[0m
[0mI (847) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 45)[0m
[0mI (853) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (862) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (871) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (1002) gpio: GPIO[47]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (1003) EYE_DISPLAY: 💡 背光已打开 (引脚: 47)[0m
[0mI (1006) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 45)[0m
[0mI (1014) EYE_DISPLAY: ⏰ 创建LVGL定时器[0m
[0mI (1018) EYE_DISPLAY: ✅ 双屏眼睛显示系统初始化完成[0m
[0mI (1024) MAIN: 🔗 连接WiFi: Xiaomi[0m
[0mI (1031) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0mI (1074) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(0)[0m
[0;32mI (1094) wifi:mode : sta (30:ed:a0:2c:cb:ec)[0m
[0;32mI (1094) wifi:enable tsf[0m
[0mI (1096) WIFI_MANAGER: Connecting to WiFi SSID: Xiaomi[0m
[0;32mI (1126) wifi:new:<7,0>, old:<1,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (1127) wifi:state: init -> auth (0xb0)[0m
[0;32mI (2131) wifi:state: auth -> init (0x200)[0m
[0;32mI (2134) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0mI (2137) WIFI_MANAGER: Retry to connect to the AP (1/5)[0m
[0mI (4553) WIFI_MANAGER: Retry to connect to the AP (2/5)[0m
[0;32mI (4607) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (4608) wifi:state: init -> auth (0xb0)[0m
[0;32mI (5611) wifi:state: auth -> init (0x200)[0m
[0;32mI (5614) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0mI (5617) WIFI_MANAGER: Retry to connect to the AP (3/5)[0m
[0mI (8033) WIFI_MANAGER: Retry to connect to the AP (4/5)[0m
[0;32mI (8088) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (8089) wifi:state: init -> auth (0xb0)[0m
[0;32mI (9092) wifi:state: auth -> init (0x200)[0m
[0;32mI (9095) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0mI (9098) WIFI_MANAGER: Retry to connect to the AP (5/5)[0m
[0mI (11512) WIFI_MANAGER: Connect to the AP failed[0m
[0mW (11513) MAIN: 📶 WiFi连接断开[0m
[0mE (11513) MAIN: ❌ WiFi连接失败[0m
[0mI (11515) MAIN: 🎬 启动双屏眼睛显示系统[0m
[0mI (11520) EYE_DISPLAY: 🔒 创建LVGL任务[0m
[0mI (11525) EYE_DISPLAY: 🚀 启动LVGL任务[0m
[0mI (11529) EYE_DISPLAY: 🎉 双屏眼睛显示系统启动完成[0m
[0mI (11536) MAIN: 🎉 系统初始化完成！[0m
[0mI (11541) MAIN: 🌟 功能特性：[0m
[0mI (11545) MAIN:    ✨ 双屏模拟眼睛效果 - 动态表情，平滑动画[0m
[0mI (11552) MAIN:    🔗 MCP WebSocket连接 - 支持小智AI远程控制[0m
[0mI (11559) MAIN:    🛠️ 工具注册系统 - 可扩展硬件设备控制[0m
[0mI (11567) MAIN:    💡 LED状态指示 - 连接状态可视化[0m
[0mI (11573) MAIN: [0m
[0mI (11575) MAIN: 💡 LED状态指示说明：[0m
[0mI (11580) MAIN:    - 快速闪烁(100ms): WiFi未连接[0m
[0mI (11586) MAIN:    - 慢速闪烁(500ms): WiFi已连接，MCP未连接[0m
[0mI (11593) MAIN:    - 常亮: WiFi和MCP均已连接[0m
[0mI (11629) EYE_DISPLAY: 👁️ 创建左眼球UI[0m
[0mI (11637) EYE_DISPLAY: ✅ 左眼球UI创建完成[0m
[0mI (11637) EYE_DISPLAY: 👁️ 创建右眼球UI[0m
[0mI (11645) EYE_DISPLAY: ✅ 右眼球UI创建完成[0m
[0mI (11646) EYE_DISPLAY: 🎉 双眼UI创建完成，开始LVGL主循环[0m
[0mI (11649) EYE_DISPLAY: 👁️ 眼睛动画系统已启动[0m
[0mI (71625) MAIN: 📊 系统状态 - WiFi: 未连接, MCP: 未连接, 剩余内存: 54220 bytes[0m
[0mI (131699) MAIN: 📊 系统状态 - WiFi: 未连接, MCP: 未连接, 剩余内存: 54248 bytes[0m
ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x17e8
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0mI (27) boot: ESP-IDF v5.4.1-dirty 2nd stage bootloader[0m
[0mI (27) boot: compile time Jul 15 2025 17:50:08[0m
[0mI (27) boot: Multicore bootloader[0m
[0mI (30) boot: chip revision: v0.2[0m
[0mI (33) boot: efuse block revision: v1.3[0m
[0mI (37) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0mI (46) boot.esp32s3: SPI Flash Size : 4MB[0m
[0mI (51) boot: Enabling RNG early entropy source...[0m
[0mI (56) boot: Partition Table:[0m
[0mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0mI (66) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0mI (74) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0mI (81) boot:  2 factory          factory app      00 00 00010000 00300000[0m
[0mI (88) boot: End of partition table[0m
[0mI (92) esp_image: segment 0: paddr=00010020 vaddr=3c0e0020 size=32bd0h (207824) map[0m
[0mI (137) esp_image: segment 1: paddr=00042bf8 vaddr=3fc9c100 size=04ec8h ( 20168) load[0m
[0mI (141) esp_image: segment 2: paddr=00047ac8 vaddr=40374000 size=08550h ( 34128) load[0m
[0mI (150) esp_image: segment 3: paddr=00050020 vaddr=42000020 size=d6d3ch (879932) map[0m
[0mI (305) esp_image: segment 4: paddr=00126d64 vaddr=4037c550 size=0fac0h ( 64192) load[0m
[0mI (320) esp_image: segment 5: paddr=0013682c vaddr=600fe100 size=0001ch (    28) load[0m
[0mI (329) boot: Loaded app from partition at offset 0x10000[0m
[0mI (329) boot: Disabling RNG early entropy source...[0m
[0mI (341) cpu_start: Multicore app[0m
[0mI (350) cpu_start: Pro cpu start user code[0m
[0mI (350) cpu_start: cpu freq: 160000000 Hz[0m
[0mI (350) app_init: Application information:[0m
[0mI (352) app_init: Project name:     esp32_mcp_hardware[0m
[0mI (358) app_init: App version:      1.0.0[0m
[0mI (362) app_init: Compile time:     Jul 15 2025 17:49:57[0m
[0mI (368) app_init: ELF file SHA256:  77bf85560...[0m
[0mI (373) app_init: ESP-IDF:          v5.4.1-dirty[0m
[0mI (378) efuse_init: Min chip rev:     v0.0[0m
[0mI (383) efuse_init: Max chip rev:     v0.99 [0m
[0mI (387) efuse_init: Chip rev:         v0.2[0m
[0mI (392) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0mI (399) heap_init: At 3FCB1000 len 00038710 (225 KiB): RAM[0m
[0mI (405) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0mI (410) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0mI (416) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0mI (423) spi_flash: detected chip: boya[0m
[0mI (427) spi_flash: flash io: dio[0m
[0mW (430) spi_flash: Detected size(16384k) larger than the size in the binary image header(4096k). Using the size in the binary image header.[0m
[0mI (444) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0mI (450) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0mI (458) main_task: Started on CPU0[0m
[0mI (462) main_task: Calling app_main()[0m
[0mI (466) MAIN: 🚀 小智 ESP32 MCP 硬件系统启动[0m
[0mI (471) MAIN: 📋 系统信息:[0m
[0mI (475) MAIN:    - 芯片型号: esp32s3[0m
[0mI (479) MAIN:    - CPU核心数: 2[0m
[0mI (483) MAIN:    - WiFi/802.11bgn[0m
[0mI (487) MAIN:    - Flash大小: 4MB[0m
[0mI (490) MAIN:    - 剩余内存: 268228 bytes[0m
[0mI (495) MAIN: [0m
[0mI (497) MAIN: 💡 初始化状态LED[0m
[0mI (502) gpio: GPIO[48]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (511) MAIN: 📶 初始化WiFi管理器[0m
[0mI (546) pp: pp rom version: e7ae62f[0m
[0mI (546) net80211: net80211 rom version: e7ae62f[0m
[0;32mI (548) wifi:wifi driver task: 3fcbd420, prio:23, stack:6656, core=0[0m
[0;32mI (565) wifi:wifi firmware version: 79fa3f41ba[0m
[0;32mI (566) wifi:wifi certification version: v7.0[0m
[0;32mI (566) wifi:config NVS flash: enabled[0m
[0;32mI (566) wifi:config nano formatting: disabled[0m
[0;32mI (570) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (575) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (579) wifi:Init management short buffer num: 32[0m
[0;32mI (584) wifi:Init static tx buffer num: 16[0m
[0;32mI (588) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (592) wifi:Init static rx buffer size: 1600[0m
[0;32mI (596) wifi:Init static rx buffer num: 10[0m
[0;32mI (599) wifi:Init dynamic rx buffer num: 32[0m
[0mI (604) wifi_init: rx ba win: 6[0m
[0mI (607) wifi_init: accept mbox: 6[0m
[0mI (611) wifi_init: tcpip mbox: 64[0m
[0mI (614) wifi_init: udp mbox: 32[0m
[0mI (618) wifi_init: tcp mbox: 32[0m
[0mI (621) wifi_init: tcp tx win: 65535[0m
[0mI (625) wifi_init: tcp rx win: 65535[0m
[0mI (629) wifi_init: tcp mss: 1440[0m
[0mI (633) wifi_init: WiFi IRAM OP enabled[0m
[0mI (637) wifi_init: WiFi RX IRAM OP enabled[0m
[0mI (642) WIFI_MANAGER: WiFi manager initialized[0m
[0mI (647) MAIN: 👁️ 初始化双屏眼睛显示系统[0m
[0mI (652) EYE_DISPLAY: 🚀 初始化双屏眼睛显示系统[0m
[0mI (658) EYE_DISPLAY: 🚌 初始化SPI总线[0m
[0mI (664) EYE_DISPLAY: 📚 初始化LVGL库[0m
[0mI (668) EYE_DISPLAY: 💾 为左屏分配显示缓冲区[0m
[0mI (673) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 2)[0m
[0mI (680) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (689) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (698) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (829) gpio: GPIO[47]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (830) EYE_DISPLAY: 💡 背光已打开 (引脚: 47)[0m
[0mI