ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x17e8
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0mI (27) boot: ESP-IDF v5.4.1-dirty 2nd stage bootloader[0m
[0mI (27) boot: compile time Jul 15 2025 17:50:08[0m
[0mI (27) boot: Multicore bootloader[0m
[0mI (30) boot: chip revision: v0.2[0m
[0mI (33) boot: efuse block revision: v1.3[0m
[0mI (37) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0mI (46) boot.esp32s3: SPI Flash Size : 4MB[0m
[0mI (51) boot: Enabling RNG early entropy source...[0m
[0mI (56) boot: Partition Table:[0m
[0mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0mI (66) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0mI (74) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0mI (81) boot:  2 factory          factory app      00 00 00010000 00300000[0m
[0mI (88) boot: End of partition table[0m
[0mI (92) esp_image: segment 0: paddr=00010020 vaddr=3c0e0020 size=32b60h (207712) map[0m
[0mI (137) esp_image: segment 1: paddr=00042b88 vaddr=3fc9c100 size=04ec8h ( 20168) load[0m
[0mI (141) esp_image: segment 2: paddr=00047a58 vaddr=40374000 size=085c0h ( 34240) load[0m
[0mI (150) esp_image: segment 3: paddr=00050020 vaddr=42000020 size=d6cc0h (879808) map[0m
[0mI (305) esp_image: segment 4: paddr=00126ce8 vaddr=4037c5c0 size=0fa50h ( 64080) load[0m
[0mI (319) esp_image: segment 5: paddr=00136740 vaddr=600fe100 size=0001ch (    28) load[0m
[0mI (329) boot: Loaded app from partition at offset 0x10000[0m
[0mI (329) boot: Disabling RNG early entropy source...[0m
[0mI (341) cpu_start: Multicore app[0m
[0mI (350) cpu_start: Pro cpu start user code[0m
[0mI (350) cpu_start: cpu freq: 160000000 Hz[0m
[0mI (350) app_init: Application information:[0m
[0mI (352) app_init: Project name:     esp32_mcp_hardware[0m
[0mI (358) app_init: App version:      1.0.0[0m
[0mI (362) app_init: Compile time:     Jul 15 2025 17:57:33[0m
[0mI (368) app_init: ELF file SHA256:  3b4ebb1a1...[0m
[0mI (373) app_init: ESP-IDF:          v5.4.1-dirty[0m
[0mI (378) efuse_init: Min chip rev:     v0.0[0m
[0mI (383) efuse_init: Max chip rev:     v0.99 [0m
[0mI (387) efuse_init: Chip rev:         v0.2[0m
[0mI (392) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0mI (399) heap_init: At 3FCB1000 len 00038710 (225 KiB): RAM[0m
[0mI (405) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0mI (410) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0mI (416) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0mI (423) spi_flash: detected chip: boya[0m
[0mI (426) spi_flash: flash io: dio[0m
[0mW (430) spi_flash: Detected size(16384k) larger than the size in the binary image header(4096k). Using the size in the binary image header.[0m
[0mI (444) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0mI (450) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0mI (458) main_task: Started on CPU0[0m
[0mI (473) main_task: Calling app_main()[0m
[0mI (474) MAIN: 🚀 小智 ESP32 MCP 硬件系统启动[0m
[0mI (474) MAIN: 📋 系统信息:[0m
[0mI (476) MAIN:    - 芯片型号: esp32s3[0m
[0mI (480) MAIN:    - CPU核心数: 2[0m
[0mI (484) MAIN:    - WiFi/802.11bgn[0m
[0mI (488) MAIN:    - Flash大小: 4MB[0m
[0mI (491) MAIN:    - 剩余内存: 268228 bytes[0m
[0mI (496) MAIN: [0m
[0mI (498) MAIN: 💡 初始化状态LED[0m
[0mI (502) gpio: GPIO[48]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (512) MAIN: 📶 初始化WiFi管理器[0m
[0mI (547) pp: pp rom version: e7ae62f[0m
[0mI (548) net80211: net80211 rom version: e7ae62f[0m
[0;32mI (549) wifi:wifi driver task: 3fcbd420, prio:23, stack:6656, core=0[0m
[0;32mI (566) wifi:wifi firmware version: 79fa3f41ba[0m
[0;32mI (566) wifi:wifi certification version: v7.0[0m
[0;32mI (567) wifi:config NVS flash: enabled[0m
[0;32mI (567) wifi:config nano formatting: disabled[0m
[0;32mI (571) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (576) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (580) wifi:Init management short buffer num: 32[0m
[0;32mI (585) wifi:Init static tx buffer num: 16[0m
[0;32mI (588) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (592) wifi:Init static rx buffer size: 1600[0m
[0;32mI (597) wifi:Init static rx buffer num: 10[0m
[0;32mI (600) wifi:Init dynamic rx buffer num: 32[0m
[0mI (605) wifi_init: rx ba win: 6[0m
[0mI (608) wifi_init: accept mbox: 6[0m
[0mI (611) wifi_init: tcpip mbox: 64[0m
[0mI (615) wifi_init: udp mbox: 32[0m
[0mI (619) wifi_init: tcp mbox: 32[0m
[0mI (622) wifi_init: tcp tx win: 65535[0m
[0mI (626) wifi_init: tcp rx win: 65535[0m
[0mI (630) wifi_init: tcp mss: 1440[0m
[0mI (634) wifi_init: WiFi IRAM OP enabled[0m
[0mI (638) wifi_init: WiFi RX IRAM OP enabled[0m
[0mI (643) WIFI_MANAGER: WiFi manager initialized[0m
[0mI (648) MAIN: 👁️ 初始化双屏眼睛显示系统[0m
[0mI (653) EYE_DISPLAY: 🚀 初始化双屏眼睛显示系统[0m
[0mI (659) EYE_DISPLAY: 🚌 初始化SPI总线[0m
[0mI (665) EYE_DISPLAY: 📚 初始化LVGL库[0m
[0mI (669) EYE_DISPLAY: 💾 为左屏分配显示缓冲区[0m
[0mI (674) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 2)[0m
[0mI (681) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (690) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (698) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (830) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 2)[0m
[0mI (835) EYE_DISPLAY: 💾 为右屏分配显示缓冲区[0m
[0mI (835) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 45)[0m
[0mI (839) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (847) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (856) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (988) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 45)[0m
[0mI (990) EYE_DISPLAY: ⏰ 创建LVGL定时器[0m
[0mI (990) EYE_DISPLAY: ✅ 双屏眼睛显示系统初始化完成[0m
[0mI (995) MAIN: 🔗 连接WiFi: Xiaomi[0m
[0mI (1001) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1044) wifi:mode : sta (30:ed:a0:2c:cb:ec)[0m
[0;32mI (1045) wifi:enable tsf[0m
[0mI (1047) WIFI_MANAGER: Connecting to WiFi SSID: Xiaomi[0m
[0;32mI (1058) wifi:new:<7,0>, old:<1,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (1059) wifi:state: init -> auth (0xb0)[0m
[0;32mI (1081) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (1125) wifi:state: assoc -> run (0x10)[0m
[0;32mI (1150) wifi:connected with Xiaomi, aid = 6, channel 7, BW20, bssid = a4:a9:30:b1:c8:be[0m
[0;32mI (1151) wifi:security: WPA2-PSK, phy: bgn, rssi: -48[0m
[0;32mI (1155) wifi:pm start, type: 1[0m

[0;32mI (1155) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (1163) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (1175) wifi:AP's beacon interval = 102400 us, DTIM period = 1[0m
[0;32mI (1185) wifi:<ba-add>idx:0 (ifx:0, a4:a9:30:b1:c8:be), tid:6, ssn:2, winSize:64[0m
[0mI (2691) esp_netif_handlers: sta ip: *************, mask: *************, gw: ************[0m
[0mI (2692) WIFI_MANAGER: Got IP:*************[0m
[0mI (2694) MAIN: 📶 WiFi连接成功[0m
[0mI (2698) MAIN: ✅ WiFi连接成功，IP地址: *************[0m
[0mI (2704) MAIN: 📶 信号强度: -49 dBm[0m
[0mI (2708) MAIN: 🔌 初始化MCP客户端[0m
[0mW (2713) websocket_client: `reconnect_timeout_ms` is not set, or it is less than or equal to zero, using default time out 10000 (milliseconds)[0m
[0mW (2726) websocket_client: `network_timeout_ms` is not set, or it is less than or equal to zero, using default time out 10000 (milliseconds)[0m
[0mI (2739) MCP_CLIENT: MCP client initialized with endpoint: wss://api.xiaozhi.me/mcp/?token=***********************************************************************************************************************************************************************************************************************************************************************[0m
[0mI (2770) MAIN: 🛠️ 注册MCP工具...[0m
[0mI (2775) MCP_CLIENT: Tool 'led_control' registered successfully[0m
[0mI (2781) MCP_CLIENT: Tool 'system_info' registered successfully[0m
[0mI (2788) MCP_CLIENT: Tool 'eye_control' registered successfully[0m
[0mI (2794) MCP_CLIENT: Tool 'calculator' registered successfully[0m
[0mI (2800) MAIN: ✅ MCP工具注册完成[0m
[0mI (2807) websocket_client: Started[0m
[0mI (2808) MCP_CLIENT: MCP client task started[0m
[0mI (2813) MCP_CLIENT: MCP client started[0m
[0mI (2817) MAIN: 🎬 启动双屏眼睛显示系统[0m
[0;32mI (2820) wifi:<ba-add>idx:1 (ifx:0, a4:a9:30:b1:c8:be), tid:0, ssn:0, winSize:64[0m
[0mI (2831) EYE_DISPLAY: 🔒 创建LVGL任务[0m
[0mI (2834) EYE_DISPLAY: 🚀 启动LVGL任务[0m
[0mI (2838) EYE_DISPLAY: 🎉 双屏眼睛显示系统启动完成[0m
[0mI (2845) MAIN: 🎉 系统初始化完成！[0m
[0mI (2850) MAIN: 🌟 功能特性：[0m
[0mI (2854) MAIN:    ✨ 双屏模拟眼睛效果 - 动态表情，平滑动画[0m
[0mI (2861) MAIN:    🔗 MCP WebSocket连接 - 支持小智AI远程控制[0m
[0mI (2868) MAIN:    🛠️ 工具注册系统 - 可扩展硬件设备控制[0m
[0mI (2876) MAIN:    💡 LED状态指示 - 连接状态可视化[0m
[0mE (2876) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0mE (2893) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0mE (2904) esp-tls: create_ssl_handle failed[0m
[0mE (2908) esp-tls: Failed to open new connection[0m
[0mE (2913) transport_base: Failed to open a new connection[0m
[0mI (2919) MAIN: [0m
[0mE (2920) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (2928) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_SETUP_FAILED, tls_error_code=0, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mI (2939) EYE_DISPLAY: 👁️ 创建左眼球UI[0m
[0mE (2945) MCP_CLIENT: WebSocket error[0m
[0mE (2954) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (2960) websocket_client: Reconnect after 10000 ms[0m
[0mI (2954) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (2965) MCP_CLIENT: WebSocket disconnected[0m
[0mI (2964) EYE_DISPLAY: ✅ 左眼球UI创建完成[0m
[0mW (2975) MAIN: 🔌 MCP服务器连接断开[0m
[0mI (2980) EYE_DISPLAY: 👁️ 创建右眼球UI[0m
[0mI (2997) EYE_DISPLAY: ✅ 右眼球UI创建完成[0m
[0mI (2998) EYE_DISPLAY: 🎉 双眼UI创建完成，开始LVGL主循环[0m
[0mI (3002) EYE_DISPLAY: 👁️ 眼睛动画系统已启动[0m
[0mI (2945) MAIN: 💡 LED状态指示说明：[0m
[0mI (3015) MAIN:    - 快速闪烁(100ms): WiFi未连接[0m
[0mI (3018) MAIN:    - 慢速闪烁(500ms): WiFi已连接，MCP未连接[0m
[0mI (3025) MAIN:    - 常亮: WiFi和MCP均已连接[0m
[0mE (7971) websocket_client: The client has started[0m
[0mI (7971) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (12971) websocket_client: The client has started[0m
[0mE (13028) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0mE (13029) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0mE (13040) esp-tls: create_ssl_handle failed[0m
[0mE (13044) esp-tls: Failed to open new connection[0m
[0mE (13049) transport_base: Failed to open a new connection[0m
[0mE (13056) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (13062) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_SETUP_FAILED, tls_error_code=0, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (13079) MCP_CLIENT: WebSocket error[0m
[0mE (13083) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (13083) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (13089) websocket_client: Reconnect after 10000 ms[0m
[0mI (13100) MCP_CLIENT: WebSocket disconnected[0m
[0mW (13105) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (18095) websocket_client: The client has started[0m
[0mI (18095) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (23095) websocket_client: The client has started[0m
[0mE (23151) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0mE (23152) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0mE (23163) esp-tls: create_ssl_handle failed[0m
[0mE (23167) esp-tls: Failed to open new connection[0m
[0mE (23172) transport_base: Failed to open a new connection[0m
[0mE (23179) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (23185) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_SETUP_FAILED, tls_error_code=0, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (23202) MCP_CLIENT: WebSocket error[0m
[0mE (23206) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (23206) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (23212) websocket_client: Reconnect after 10000 ms[0m
[0mI (23223) MCP_CLIENT: WebSocket disconnected[0m
[0mW (23228) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (28218) websocket_client: The client has started[0m
[0mI (28218) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (33218) websocket_client: The client has started[0m
[0mE (33272) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0mE (33272) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0mE (33283) esp-tls: create_ssl_handle failed[0m
[0mE (33288) esp-tls: Failed to open new connection[0m
[0mE (33293) transport_base: Failed to open a new connection[0m
[0mE (33300) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (33305) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_SETUP_FAILED, tls_error_code=0, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (33323) MCP_CLIENT: WebSocket error[0m
[0mE (33327) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (33327) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (33332) websocket_client: Reconnect after 10000 ms[0m
[0mI (33344) MCP_CLIENT: WebSocket disconnected[0m
[0mW (33348) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (38338) websocket_client: The client has started[0m
[0mI (38338) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (43338) websocket_client: The client has started[0m
[0mE (43398) esp-tls-mbedtls: No server verification option set in esp_tls_cfg_t structure. Check esp_tls API reference[0m
[0mE (43399) esp-tls-mbedtls: Failed to set client configurations, returned [0x8017] (ESP_ERR_MBEDTLS_SSL_SETUP_FAILED)[0m
[0mE (43410) esp-tls: create_ssl_handle failed[0m
[0mE (43414) esp-tls: Failed to open new connection[0m
[0mE (43419) transport_base: Failed to open a new connection[0m
[0mE (43426) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (43432) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_SETUP_FAILED, tls_error_code=0, tls_flags=0, esp_ws_handshake_status_code=