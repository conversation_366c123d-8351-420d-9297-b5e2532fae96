[1/5] cd /Users/<USER>/code/esp32-mcp-hardware/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/code/esp32-mcp-hardware/build/partition_table/partition-table.bin /Users/<USER>/code/esp32-mcp-hardware/build/esp32_mcp_hardware.bin
esp32_mcp_hardware.bin binary size 0x126870 bytes. Smallest app partition is 0x300000 bytes. 0x1d9790 bytes (62%) free.
[2/5] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/bootloader.bin
Bootloader binary size 0x5470 bytes. 0x2b90 bytes (34%) free.
[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] cd /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/esp/v5.4.1/esp-idf -D "SERIAL_TOOL=/Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python;;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3" -D "SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args" -D WORKING_DIRECTORY=/Users/<USER>/code/esp32-mcp-hardware/build -P /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake
esptool.py --chip esp32s3 -p /dev/cu.wchusbserial5A4B0192731 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 4MB 0x0 bootloader/bootloader.bin 0x10000 esp32_mcp_hardware.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.9.0
Serial port /dev/cu.wchusbserial5A4B0192731
Connecting....
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Embedded PSRAM 8MB (AP_3v3)
Crystal is 40MHz
MAC: 30:ed:a0:2c:cb:ec
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x00136fff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 21616 bytes to 13435...
Writing at 0x00000000... (100 %)
Wrote 21616 bytes (13435 compressed) at 0x00000000 in 0.7 seconds (effective 264.0 kbit/s)...
Hash of data verified.
Compressed 1206384 bytes to 734371...
Writing at 0x00010000... (2 %)
Writing at 0x0001c8a8... (4 %)
Writing at 0x0002a3da... (6 %)
Writing at 0x000320dc... (8 %)
Writing at 0x0003cb4c... (11 %)
Writing at 0x000499ca... (13 %)
Writing at 0x0004f613... (15 %)
Writing at 0x0005532f... (17 %)
Writing at 0x0005bb91... (20 %)
Writing at 0x00061c18... (22 %)
Writing at 0x00067fd3... (24 %)
Writing at 0x0006db25... (26 %)
Writing at 0x00073d23... (28 %)
Writing at 0x00079f96... (31 %)
Writing at 0x0007fe3b... (33 %)
Writing at 0x00085c53... (35 %)
Writing at 0x0008c0d7... (37 %)
Writing at 0x000921df... (40 %)
Writing at 0x0009826a... (42 %)
Writing at 0x0009e247... (44 %)
Writing at 0x000a3fe7... (46 %)
Writing at 0x000aa23e... (48 %)
Writing at 0x000b03dc... (51 %)
Writing at 0x000b5909... (53 %)
Writing at 0x000ba861... (55 %)
Writing at 0x000bf9d2... (57 %)
Writing at 0x000c4e64... (60 %)
Writing at 0x000ca05f... (62 %)
Writing at 0x000cf249... (64 %)
Writing at 0x000d45b3... (66 %)
Writing at 0x000da75f... (68 %)
Writing at 0x000e01ae... (71 %)
Writing at 0x000e5e51... (73 %)
Writing at 0x000eba53... (75 %)
Writing at 0x000f155a... (77 %)
Writing at 0x000f6f13... (80 %)
Writing at 0x000fcca7... (82 %)
Writing at 0x00102776... (84 %)
Writing at 0x00107c36... (86 %)
Writing at 0x0010dc49... (88 %)
Writing at 0x00117d52... (91 %)
Writing at 0x0011fab8... (93 %)
Writing at 0x00125737... (95 %)
Writing at 0x0012bebe... (97 %)
Writing at 0x00131dc4... (100 %)
Wrote 1206384 bytes (734371 compressed) at 0x00010000 in 17.2 seconds (effective 562.0 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 105...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (105 compressed) at 0x00008000 in 0.1 seconds (effective 431.5 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
