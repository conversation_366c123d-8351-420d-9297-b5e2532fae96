ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x17e8
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0mI (27) boot: ESP-IDF v5.4.1-dirty 2nd stage bootloader[0m
[0mI (27) boot: compile time Jul 15 2025 17:50:08[0m
[0mI (27) boot: Multicore bootloader[0m
[0mI (30) boot: chip revision: v0.2[0m
[0mI (33) boot: efuse block revision: v1.3[0m
[0mI (37) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0mI (46) boot.esp32s3: SPI Flash Size : 4MB[0m
[0mI (51) boot: Enabling RNG early entropy source...[0m
[0mI (56) boot: Partition Table:[0m
[0mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0mI (66) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0mI (74) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0mI (81) boot:  2 factory          factory app      00 00 00010000 00300000[0m
[0mI (88) boot: End of partition table[0m
[0mI (92) esp_image: segment 0: paddr=00010020 vaddr=3c0e0020 size=32af0h (207600) map[0m
[0mI (137) esp_image: segment 1: paddr=00042b18 vaddr=3fc9c100 size=04ec8h ( 20168) load[0m
[0mI (141) esp_image: segment 2: paddr=000479e8 vaddr=40374000 size=08630h ( 34352) load[0m
[0mI (150) esp_image: segment 3: paddr=00050020 vaddr=42000020 size=d6ca0h (879776) map[0m
[0mI (305) esp_image: segment 4: paddr=00126cc8 vaddr=4037c630 size=0f9e0h ( 63968) load[0m
[0mI (319) esp_image: segment 5: paddr=001366b0 vaddr=600fe100 size=0001ch (    28) load[0m
[0mI (329) boot: Loaded app from partition at offset 0x10000[0m
[0mI (329) boot: Disabling RNG early entropy source...[0m
[0mI (341) cpu_start: Multicore app[0m
[0mI (350) cpu_start: Pro cpu start user code[0m
[0mI (350) cpu_start: cpu freq: 160000000 Hz[0m
[0mI (350) app_init: Application information:[0m
[0mI (352) app_init: Project name:     esp32_mcp_hardware[0m
[0mI (358) app_init: App version:      1.0.0[0m
[0mI (362) app_init: Compile time:     Jul 15 2025 18:06:47[0m
[0mI (368) app_init: ELF file SHA256:  25f4af0b4...[0m
[0mI (373) app_init: ESP-IDF:          v5.4.1-dirty[0m
[0mI (378) efuse_init: Min chip rev:     v0.0[0m
[0mI (382) efuse_init: Max chip rev:     v0.99 [0m
[0mI (387) efuse_init: Chip rev:         v0.2[0m
[0mI (392) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0mI (399) heap_init: At 3FCB1000 len 00038710 (225 KiB): RAM[0m
[0mI (405) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0mI (410) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0mI (416) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0mI (423) spi_flash: detected chip: boya[0m
[0mI (426) spi_flash: flash io: dio[0m
[0mW (430) spi_flash: Detected size(16384k) larger than the size in the binary image header(4096k). Using the size in the binary image header.[0m
[0mI (444) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0mI (450) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0mI (458) main_task: Started on CPU0[0m
[0mI (473) main_task: Calling app_main()[0m
[0mI (474) MAIN: 🚀 小智 ESP32 MCP 硬件系统启动[0m
[0mI (474) MAIN: 📋 系统信息:[0m
[0mI (476) MAIN:    - 芯片型号: esp32s3[0m
[0mI (480) MAIN:    - CPU核心数: 2[0m
[0mI (484) MAIN:    - WiFi/802.11bgn[0m
[0mI (488) MAIN:    - Flash大小: 4MB[0m
[0mI (491) MAIN:    - 剩余内存: 268228 bytes[0m
[0mI (496) MAIN: [0m
[0mI (498) MAIN: 💡 初始化状态LED[0m
[0mI (502) gpio: GPIO[48]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (512) MAIN: 📶 初始化WiFi管理器[0m
[0mI (547) pp: pp rom version: e7ae62f[0m
[0mI (547) net80211: net80211 rom version: e7ae62f[0m
[0;32mI (549) wifi:wifi driver task: 3fcbd420, prio:23, stack:6656, core=0[0m
[0;32mI (566) wifi:wifi firmware version: 79fa3f41ba[0m
[0;32mI (566) wifi:wifi certification version: v7.0[0m
[0;32mI (566) wifi:config NVS flash: enabled[0m
[0;32mI (567) wifi:config nano formatting: disabled[0m
[0;32mI (571) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (576) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (580) wifi:Init management short buffer num: 32[0m
[0;32mI (585) wifi:Init static tx buffer num: 16[0m
[0;32mI (588) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (592) wifi:Init static rx buffer size: 1600[0m
[0;32mI (596) wifi:Init static rx buffer num: 10[0m
[0;32mI (600) wifi:Init dynamic rx buffer num: 32[0m
[0mI (605) wifi_init: rx ba win: 6[0m
[0mI (607) wifi_init: accept mbox: 6[0m
[0mI (611) wifi_init: tcpip mbox: 64[0m
[0mI (615) wifi_init: udp mbox: 32[0m
[0mI (618) wifi_init: tcp mbox: 32[0m
[0mI (622) wifi_init: tcp tx win: 65535[0m
[0mI (626) wifi_init: tcp rx win: 65535[0m
[0mI (630) wifi_init: tcp mss: 1440[0m
[0mI (634) wifi_init: WiFi IRAM OP enabled[0m
[0mI (638) wifi_init: WiFi RX IRAM OP enabled[0m
[0mI (643) WIFI_MANAGER: WiFi manager initialized[0m
[0mI (647) MAIN: 👁️ 初始化双屏眼睛显示系统[0m
[0mI (653) EYE_DISPLAY: 🚀 初始化双屏眼睛显示系统[0m
[0mI (659) EYE_DISPLAY: 🚌 初始化SPI总线[0m
[0mI (664) EYE_DISPLAY: 📚 初始化LVGL库[0m
[0mI (668) EYE_DISPLAY: 💾 为左屏分配显示缓冲区[0m
[0mI (674) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 2)[0m
[0mI (681) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (689) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (698) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (830) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 2)[0m
[0mI (835) EYE_DISPLAY: 💾 为右屏分配显示缓冲区[0m
[0mI (835) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 45)[0m
[0mI (839) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (847) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (856) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (988) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 45)[0m
[0mI (990) EYE_DISPLAY: ⏰ 创建LVGL定时器[0m
[0mI (990) EYE_DISPLAY: ✅ 双屏眼睛显示系统初始化完成[0m
[0mI (995) MAIN: 🔗 连接WiFi: Xiaomi[0m
[0mI (1001) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0;32mI (1043) wifi:mode : sta (30:ed:a0:2c:cb:ec)[0m
[0;32mI (1044) wifi:enable tsf[0m
[0mI (1045) WIFI_MANAGER: Connecting to WiFi SSID: Xiaomi[0m
[0;32mI (1080) wifi:new:<7,0>, old:<1,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (1081) wifi:state: init -> auth (0xb0)[0m
[0;32mI (2084) wifi:state: auth -> init (0x200)[0m
[0;32mI (2087) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0mI (2090) WIFI_MANAGER: Retry to connect to the AP (1/5)[0m
[0mI (4505) WIFI_MANAGER: Retry to connect to the AP (2/5)[0m
[0;32mI (4561) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (4562) wifi:state: init -> auth (0xb0)[0m
[0;32mI (5565) wifi:state: auth -> init (0x200)[0m
[0;32mI (5568) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0mI (5571) WIFI_MANAGER: Retry to connect to the AP (3/5)[0m
[0mI (7986) WIFI_MANAGER: Retry to connect to the AP (4/5)[0m
[0;32mI (8043) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (8044) wifi:state: init -> auth (0xb0)[0m
[0;32mI (9047) wifi:state: auth -> init (0x200)[0m
[0;32mI (9050) wifi:new:<7,0>, old:<7,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0mI (9053) WIFI_MANAGER: Retry to connect to the AP (5/5)[0m
[0mI (11467) WIFI_MANAGER: Connect to the AP failed[0m
[0mW (11467)