{"idf.port": "/dev/tty.wchusbserial5A4B0192731", "idf.openOcdConfigs": ["board/esp32s3-builtin.cfg"], "idf.customExtraVars": {"IDF_TARGET": "esp32s3"}, "clangd.path": "/Users/<USER>/.espressif/tools/esp-clang/esp-18.1.2_20240912/esp-clang/bin/clangd", "clangd.arguments": ["--background-index", "--query-driver=/Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc", "--compile-commands-dir=/Users/<USER>/code/esp32-mcp-hardware/build"], "idf.flashType": "UART"}