/**
 * @file mcp_client.h
 * @brief MCP WebSocket客户端头文件
 * 
 * ESP-IDF版本的WebSocket MCP客户端
 * 支持工具注册、远程调用、连接管理
 */

#ifndef MCP_CLIENT_H
#define MCP_CLIENT_H

#include <stdio.h>
#include <string.h>
#include <stdbool.h>
#include <stdint.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_websocket_client.h"
#include "esp_log.h"
#include "esp_system.h"
#include "cJSON.h"
#include "app_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== 常量定义 ====================

#define MCP_MAX_TOOLS           10
#define MCP_MAX_TOOL_NAME_LEN   64
#define MCP_MAX_TOOL_DESC_LEN   256
#define MCP_MAX_SCHEMA_LEN      1024
#define MCP_MAX_MESSAGE_LEN     2048

// ==================== 工具响应结构 ====================

typedef struct {
    char *content;      // 响应内容(JSON字符串)
    bool is_error;      // 是否为错误响应
} mcp_tool_response_t;

// ==================== 工具回调函数类型 ====================

typedef mcp_tool_response_t (*mcp_tool_callback_t)(const char* args);

// ==================== 工具结构体 ====================

typedef struct {
    char name[MCP_MAX_TOOL_NAME_LEN];           // 工具名称
    char description[MCP_MAX_TOOL_DESC_LEN];    // 工具描述
    char input_schema[MCP_MAX_SCHEMA_LEN];      // 输入schema(JSON格式)
    mcp_tool_callback_t callback;               // 工具回调函数
    bool active;                                // 是否激活
} mcp_tool_t;

// ==================== MCP客户端状态 ====================

typedef enum {
    MCP_STATE_DISCONNECTED,
    MCP_STATE_CONNECTING,
    MCP_STATE_CONNECTED,
    MCP_STATE_ERROR
} mcp_client_state_t;

// ==================== MCP客户端结构体 ====================

typedef struct {
    esp_websocket_client_handle_t client;
    mcp_client_state_t state;
    mcp_tool_t tools[MCP_MAX_TOOLS];
    int tool_count;
    char endpoint[256];
    uint32_t request_id;
    bool auto_reconnect;
    uint32_t last_ping_time;
    TaskHandle_t task_handle;
    EventGroupHandle_t event_group;
} mcp_client_t;

// ==================== 事件位定义 ====================

#define MCP_CONNECTED_BIT       BIT0
#define MCP_DISCONNECTED_BIT    BIT1
#define MCP_ERROR_BIT           BIT2

// ==================== 回调函数类型 ====================

typedef void (*mcp_connection_callback_t)(bool connected);
typedef void (*mcp_message_callback_t)(const char* message);
typedef void (*mcp_error_callback_t)(const char* error);

// ==================== 函数声明 ====================

/**
 * @brief 初始化MCP客户端
 * @param endpoint WebSocket服务器地址
 * @return esp_err_t 初始化结果
 */
esp_err_t mcp_client_init(const char* endpoint);

/**
 * @brief 启动MCP客户端连接
 * @return esp_err_t 启动结果
 */
esp_err_t mcp_client_start(void);

/**
 * @brief 停止MCP客户端
 */
void mcp_client_stop(void);

/**
 * @brief 注册工具
 * @param name 工具名称
 * @param description 工具描述
 * @param input_schema 输入schema(JSON格式)
 * @param callback 工具回调函数
 * @return esp_err_t 注册结果
 */
esp_err_t mcp_client_register_tool(const char* name, 
                                   const char* description,
                                   const char* input_schema,
                                   mcp_tool_callback_t callback);

/**
 * @brief 注销工具
 * @param name 工具名称
 * @return esp_err_t 注销结果
 */
esp_err_t mcp_client_unregister_tool(const char* name);

/**
 * @brief 发送消息到MCP服务器
 * @param message 消息内容
 * @return esp_err_t 发送结果
 */
esp_err_t mcp_client_send_message(const char* message);

/**
 * @brief 获取客户端状态
 * @return mcp_client_state_t 当前状态
 */
mcp_client_state_t mcp_client_get_state(void);

/**
 * @brief 获取已注册工具数量
 * @return int 工具数量
 */
int mcp_client_get_tool_count(void);

/**
 * @brief 检查是否已连接
 * @return true 已连接
 * @return false 未连接
 */
bool mcp_client_is_connected(void);

/**
 * @brief 设置连接状态回调
 * @param callback 回调函数
 */
void mcp_client_set_connection_callback(mcp_connection_callback_t callback);

/**
 * @brief 设置消息接收回调
 * @param callback 回调函数
 */
void mcp_client_set_message_callback(mcp_message_callback_t callback);

/**
 * @brief 设置错误回调
 * @param callback 回调函数
 */
void mcp_client_set_error_callback(mcp_error_callback_t callback);

/**
 * @brief 创建工具响应
 * @param content 响应内容
 * @param is_error 是否为错误
 * @return mcp_tool_response_t 工具响应
 */
mcp_tool_response_t mcp_create_response(const char* content, bool is_error);

/**
 * @brief 释放工具响应
 * @param response 工具响应
 */
void mcp_free_response(mcp_tool_response_t* response);

#ifdef __cplusplus
}
#endif

#endif // MCP_CLIENT_H
