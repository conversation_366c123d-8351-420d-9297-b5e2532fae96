[1/5] cd /Users/<USER>/code/esp32-mcp-hardware/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/code/esp32-mcp-hardware/build/partition_table/partition-table.bin /Users/<USER>/code/esp32-mcp-hardware/build/esp32_mcp_hardware.bin
esp32_mcp_hardware.bin binary size 0x1266f0 bytes. Smallest app partition is 0x300000 bytes. 0x1d9910 bytes (62%) free.
[2/5] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/bootloader.bin
Bootloader binary size 0x5470 bytes. 0x2b90 bytes (34%) free.
[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] cd /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/esp/v5.4.1/esp-idf -D "SERIAL_TOOL=/Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python;;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3" -D "SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args" -D WORKING_DIRECTORY=/Users/<USER>/code/esp32-mcp-hardware/build -P /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake
esptool.py --chip esp32s3 -p /dev/cu.wchusbserial5A4B0192731 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 4MB 0x0 bootloader/bootloader.bin 0x10000 esp32_mcp_hardware.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.9.0
Serial port /dev/cu.wchusbserial5A4B0192731
Connecting...
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Embedded PSRAM 8MB (AP_3v3)
Crystal is 40MHz
MAC: 30:ed:a0:2c:cb:ec
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x00136fff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 21616 bytes to 13435...
Writing at 0x00000000... (100 %)
Wrote 21616 bytes (13435 compressed) at 0x00000000 in 0.7 seconds (effective 262.0 kbit/s)...
Hash of data verified.
Compressed 1206000 bytes to 734211...
Writing at 0x00010000... (2 %)
Writing at 0x0001c8f7... (4 %)
Writing at 0x0002a3da... (6 %)
Writing at 0x00032091... (8 %)
Writing at 0x0003cb7a... (11 %)
Writing at 0x0004993f... (13 %)
Writing at 0x0004f606... (15 %)
Writing at 0x00055315... (17 %)
Writing at 0x0005bb65... (20 %)
Writing at 0x00061be9... (22 %)
Writing at 0x00067fa2... (24 %)
Writing at 0x0006daec... (26 %)
Writing at 0x00073d11... (28 %)
Writing at 0x00079f8e... (31 %)
Writing at 0x0007fe09... (33 %)
Writing at 0x00085c2f... (35 %)
Writing at 0x0008c0bf... (37 %)
Writing at 0x0009219a... (40 %)
Writing at 0x0009820a... (42 %)
Writing at 0x0009e1e8... (44 %)
Writing at 0x000a3f89... (46 %)
Writing at 0x000aa1ed... (48 %)
Writing at 0x000b038f... (51 %)
Writing at 0x000b58b3... (53 %)
Writing at 0x000ba807... (55 %)
Writing at 0x000bf992... (57 %)
Writing at 0x000c4e0d... (60 %)
Writing at 0x000c9ff7... (62 %)
Writing at 0x000cf1f4... (64 %)
Writing at 0x000d454a... (66 %)
Writing at 0x000da700... (68 %)
Writing at 0x000e014f... (71 %)
Writing at 0x000e5df5... (73 %)
Writing at 0x000eb9fa... (75 %)
Writing at 0x000f14f0... (77 %)
Writing at 0x000f6ecf... (80 %)
Writing at 0x000fcc49... (82 %)
Writing at 0x00102751... (84 %)
Writing at 0x00107bd6... (86 %)
Writing at 0x0010dbef... (88 %)
Writing at 0x00117ce9... (91 %)
Writing at 0x0011fa62... (93 %)
Writing at 0x00125706... (95 %)
Writing at 0x0012be19... (97 %)
Writing at 0x00131d17... (100 %)
Wrote 1206000 bytes (734211 compressed) at 0x00010000 in 17.3 seconds (effective 558.6 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 105...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (105 compressed) at 0x00008000 in 0.1 seconds (effective 416.2 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
