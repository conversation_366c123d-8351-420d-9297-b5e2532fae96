/**
 * @file wifi_manager.c
 * @brief WiFi连接管理实现
 * 
 * ESP32 WiFi连接管理，支持自动重连
 */

#include "common/wifi_manager.h"

static const char *TAG = "WIFI_MANAGER";

// WiFi管理器状态
static wifi_state_t g_wifi_state = WIFI_STATE_DISCONNECTED;
static EventGroupHandle_t g_wifi_event_group = NULL;
static wifi_connection_callback_t g_connection_callback = NULL;
static int g_retry_count = 0;

// 前向声明
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data);

esp_err_t wifi_manager_init(void)
{
    // 初始化NVS
    esp_err_t ret = nvs_flash_init();
    if (ret == ESP_ERR_NVS_NO_FREE_PAGES || ret == ESP_ERR_NVS_NEW_VERSION_FOUND) {
        ESP_ERROR_CHECK(nvs_flash_erase());
        ret = nvs_flash_init();
    }
    ESP_ERROR_CHECK(ret);

    // 创建事件组
    g_wifi_event_group = xEventGroupCreate();
    if (!g_wifi_event_group) {
        ESP_LOGE(TAG, "Failed to create event group");
        return ESP_ERR_NO_MEM;
    }

    // 初始化网络接口
    ESP_ERROR_CHECK(esp_netif_init());
    ESP_ERROR_CHECK(esp_event_loop_create_default());
    esp_netif_create_default_wifi_sta();

    // 初始化WiFi
    wifi_init_config_t cfg = WIFI_INIT_CONFIG_DEFAULT();
    ESP_ERROR_CHECK(esp_wifi_init(&cfg));

    // 注册事件处理器
    ESP_ERROR_CHECK(esp_event_handler_instance_register(WIFI_EVENT,
                                                        ESP_EVENT_ANY_ID,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        NULL));
    ESP_ERROR_CHECK(esp_event_handler_instance_register(IP_EVENT,
                                                        IP_EVENT_STA_GOT_IP,
                                                        &wifi_event_handler,
                                                        NULL,
                                                        NULL));

    ESP_LOGI(TAG, "WiFi manager initialized");
    return ESP_OK;
}

esp_err_t wifi_manager_start(const char* ssid, const char* password)
{
    if (!ssid || !password) {
        ESP_LOGE(TAG, "SSID and password cannot be NULL");
        return ESP_ERR_INVALID_ARG;
    }

    if (g_wifi_state == WIFI_STATE_CONNECTED) {
        ESP_LOGW(TAG, "WiFi already connected");
        return ESP_OK;
    }

    g_wifi_state = WIFI_STATE_CONNECTING;
    g_retry_count = 0;

    // 配置WiFi
    wifi_config_t wifi_config = {
        .sta = {
            .threshold.authmode = WIFI_AUTH_WPA2_PSK,
            .pmf_cfg = {
                .capable = true,
                .required = false
            },
        },
    };

    strncpy((char*)wifi_config.sta.ssid, ssid, sizeof(wifi_config.sta.ssid) - 1);
    strncpy((char*)wifi_config.sta.password, password, sizeof(wifi_config.sta.password) - 1);

    ESP_ERROR_CHECK(esp_wifi_set_mode(WIFI_MODE_STA));
    ESP_ERROR_CHECK(esp_wifi_set_config(WIFI_IF_STA, &wifi_config));
    ESP_ERROR_CHECK(esp_wifi_start());

    ESP_LOGI(TAG, "Connecting to WiFi SSID: %s", ssid);
    return ESP_OK;
}

void wifi_manager_stop(void)
{
    if (g_wifi_state == WIFI_STATE_DISCONNECTED) {
        return;
    }

    g_wifi_state = WIFI_STATE_DISCONNECTED;
    esp_wifi_stop();
    ESP_LOGI(TAG, "WiFi stopped");
}

wifi_state_t wifi_manager_get_state(void)
{
    return g_wifi_state;
}

bool wifi_manager_is_connected(void)
{
    return g_wifi_state == WIFI_STATE_CONNECTED;
}

esp_err_t wifi_manager_get_ip(char* ip_str, size_t len)
{
    if (!ip_str || len == 0) {
        return ESP_ERR_INVALID_ARG;
    }

    if (g_wifi_state != WIFI_STATE_CONNECTED) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_netif_ip_info_t ip_info;
    esp_netif_t *netif = esp_netif_get_handle_from_ifkey("WIFI_STA_DEF");
    if (!netif) {
        return ESP_ERR_INVALID_STATE;
    }

    esp_err_t ret = esp_netif_get_ip_info(netif, &ip_info);
    if (ret == ESP_OK) {
        snprintf(ip_str, len, IPSTR, IP2STR(&ip_info.ip));
    }
    return ret;
}

int8_t wifi_manager_get_rssi(void)
{
    if (g_wifi_state != WIFI_STATE_CONNECTED) {
        return -100; // 表示未连接
    }

    wifi_ap_record_t ap_info;
    esp_err_t ret = esp_wifi_sta_get_ap_info(&ap_info);
    if (ret == ESP_OK) {
        return ap_info.rssi;
    }
    return -100;
}

void wifi_manager_set_callback(wifi_connection_callback_t callback)
{
    g_connection_callback = callback;
}

bool wifi_manager_wait_connected(uint32_t timeout_ms)
{
    if (g_wifi_state == WIFI_STATE_CONNECTED) {
        return true;
    }

    EventBits_t bits = xEventGroupWaitBits(g_wifi_event_group,
                                           WIFI_CONNECTED_BIT | WIFI_FAIL_BIT,
                                           pdFALSE,
                                           pdFALSE,
                                           pdMS_TO_TICKS(timeout_ms));

    return (bits & WIFI_CONNECTED_BIT) != 0;
}

// WiFi事件处理器
static void wifi_event_handler(void* arg, esp_event_base_t event_base, int32_t event_id, void* event_data)
{
    if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_START) {
        esp_wifi_connect();
    } else if (event_base == WIFI_EVENT && event_id == WIFI_EVENT_STA_DISCONNECTED) {
        if (g_retry_count < WIFI_MAXIMUM_RETRY) {
            esp_wifi_connect();
            g_retry_count++;
            ESP_LOGI(TAG, "Retry to connect to the AP (%d/%d)", g_retry_count, WIFI_MAXIMUM_RETRY);
        } else {
            xEventGroupSetBits(g_wifi_event_group, WIFI_FAIL_BIT);
            g_wifi_state = WIFI_STATE_ERROR;
            ESP_LOGI(TAG, "Connect to the AP failed");
            
            if (g_connection_callback) {
                g_connection_callback(false);
            }
        }
    } else if (event_base == IP_EVENT && event_id == IP_EVENT_STA_GOT_IP) {
        ip_event_got_ip_t* event = (ip_event_got_ip_t*) event_data;
        ESP_LOGI(TAG, "Got IP:" IPSTR, IP2STR(&event->ip_info.ip));
        g_retry_count = 0;
        g_wifi_state = WIFI_STATE_CONNECTED;
        xEventGroupSetBits(g_wifi_event_group, WIFI_CONNECTED_BIT);
        
        if (g_connection_callback) {
            g_connection_callback(true);
        }
    }
}
