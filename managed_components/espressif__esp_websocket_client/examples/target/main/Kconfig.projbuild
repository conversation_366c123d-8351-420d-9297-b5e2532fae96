menu "Example Configuration"

    choice WEBSOCKET_URI_SOURCE
        prompt "Websocket URI source"
        default WEBSOCKET_URI_FROM_STRING
        help
            Selects the source of the URI used in the example.

        config WEBSOCKET_URI_FROM_STRING
            bool "From string"

        config WEBSOCKET_URI_FROM_STDIN
            bool "From stdin"
    endchoice

    config WEBSOCKET_URI
        string "Websocket endpoint URI"
        depends on WEBSOCKET_URI_FROM_STRING
        default "wss://echo.websocket.events"
        help
            URL of websocket endpoint this example connects to and sends echo

    config WS_OVER_TLS_SERVER_AUTH
        bool "Enable WebSocket over TLS with Server Certificate Verification Only"
        default y
        help
            Enables WebSocket connections over TLS (WSS) with server certificate verification.
            This setting mandates the client to verify the servers certificate, while the server
            does not require client certificate verification.

    config WS_OVER_TLS_MUTUAL_AUTH
        bool "Enable WebSocket over TLS with Server Client Mutual Authentification"
        default n
        help
            Enables WebSocket connections over TLS (WSS) with server and client mutual certificate verification.

    config WS_OVER_TLS_SKIP_COMMON_NAME_CHECK
        bool "Skip common name(CN) check during TLS authentification"
        default n
        help
            Skipping Common Name(CN) check during TLS(WSS) authentificatio

    if CONFIG_IDF_TARGET = "linux"
      config GCOV_ENABLED
          bool "Coverage analyzer"
          default n
          help
              Enables coverage analyzing for host tests.
      endif
endmenu
