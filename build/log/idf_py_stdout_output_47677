[1/5] cd /Users/<USER>/code/esp32-mcp-hardware/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/code/esp32-mcp-hardware/build/partition_table/partition-table.bin /Users/<USER>/code/esp32-mcp-hardware/build/esp32_mcp_hardware.bin
esp32_mcp_hardware.bin binary size 0x126780 bytes. Smallest app partition is 0x300000 bytes. 0x1d9880 bytes (62%) free.
[2/5] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/bootloader.bin
Bootloader binary size 0x5470 bytes. 0x2b90 bytes (34%) free.
[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] cd /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/esp/v5.4.1/esp-idf -D "SERIAL_TOOL=/Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python;;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3" -D "SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args" -D WORKING_DIRECTORY=/Users/<USER>/code/esp32-mcp-hardware/build -P /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake
esptool.py --chip esp32s3 -p /dev/cu.wchusbserial5A4B0192731 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 4MB 0x0 bootloader/bootloader.bin 0x10000 esp32_mcp_hardware.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.9.0
Serial port /dev/cu.wchusbserial5A4B0192731
Connecting...
Chip is ESP32-S3 (QFN56) (revision v0.2)
Features: WiFi, BLE, Embedded PSRAM 8MB (AP_3v3)
Crystal is 40MHz
MAC: 30:ed:a0:2c:cb:ec
Uploading stub...
Running stub...
Stub running...
Changing baud rate to 460800
Changed.
Configuring flash size...
Flash will be erased from 0x00000000 to 0x00005fff...
Flash will be erased from 0x00010000 to 0x00136fff...
Flash will be erased from 0x00008000 to 0x00008fff...
SHA digest in image updated
Compressed 21616 bytes to 13435...
Writing at 0x00000000... (100 %)
Wrote 21616 bytes (13435 compressed) at 0x00000000 in 0.6 seconds (effective 268.5 kbit/s)...
Hash of data verified.
Compressed 1206144 bytes to 734256...
Writing at 0x00010000... (2 %)
Writing at 0x0001c918... (4 %)
Writing at 0x0002a3e6... (6 %)
Writing at 0x000320b8... (8 %)
Writing at 0x0003cbab... (11 %)
Writing at 0x00049978... (13 %)
Writing at 0x0004f625... (15 %)
Writing at 0x00055325... (17 %)
Writing at 0x0005bb85... (20 %)
Writing at 0x00061bfd... (22 %)
Writing at 0x00067fc3... (24 %)
Writing at 0x0006dafe... (26 %)
Writing at 0x00073d4b... (28 %)
Writing at 0x00079f9f... (31 %)
Writing at 0x0007fe21... (33 %)
Writing at 0x00085c4a... (35 %)
Writing at 0x0008c0c9... (37 %)
Writing at 0x000921b1... (40 %)
Writing at 0x00098232... (42 %)
Writing at 0x0009e206... (44 %)
Writing at 0x000a3fa4... (46 %)
Writing at 0x000aa207... (48 %)
Writing at 0x000b03a8... (51 %)
Writing at 0x000b58ca... (53 %)
Writing at 0x000ba828... (55 %)
Writing at 0x000bf9c9... (57 %)
Writing at 0x000c4e42... (60 %)
Writing at 0x000ca022... (62 %)
Writing at 0x000cf22d... (64 %)
Writing at 0x000d4577... (66 %)
Writing at 0x000da72d... (68 %)
Writing at 0x000e0196... (71 %)
Writing at 0x000e5e2a... (73 %)
Writing at 0x000eba2f... (75 %)
Writing at 0x000f151f... (77 %)
Writing at 0x000f6efd... (80 %)
Writing at 0x000fcc72... (82 %)
Writing at 0x0010277d... (84 %)
Writing at 0x00107c00... (86 %)
Writing at 0x0010dc21... (88 %)
Writing at 0x00117d0e... (91 %)
Writing at 0x0011fa86... (93 %)
Writing at 0x00125734... (95 %)
Writing at 0x0012be71... (97 %)
Writing at 0x00131d71... (100 %)
Wrote 1206144 bytes (734256 compressed) at 0x00010000 in 17.2 seconds (effective 559.4 kbit/s)...
Hash of data verified.
Compressed 3072 bytes to 105...
Writing at 0x00008000... (100 %)
Wrote 3072 bytes (105 compressed) at 0x00008000 in 0.1 seconds (effective 416.8 kbit/s)...
Hash of data verified.

Leaving...
Hard resetting via RTS pin...
