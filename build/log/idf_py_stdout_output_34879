-- IDF_TARGET is not set, guessed 'esp32s3' from sdkconfig '/Users/<USER>/code/esp32-mcp-hardware/sdkconfig'
-- Found Git: /usr/bin/git (found version "2.39.5 (Apple Git-154)")
-- The C compiler identification is GNU 14.2.0
-- The CXX compiler identification is GNU 14.2.0
-- The ASM compiler identification is GNU
-- Found assembler: /Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc
-- Detecting C compiler ABI info
-- Detecting C compiler ABI info - done
-- Check for working C compiler: /Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-gcc - skipped
-- Detecting C compile features
-- Detecting C compile features - done
-- Detecting CXX compiler <PERSON><PERSON> info
-- Detecting CXX compiler ABI info - done
-- Check for working CXX compiler: /Users/<USER>/.espressif/tools/xtensa-esp-elf/esp-14.2.0_20241119/xtensa-esp-elf/bin/xtensa-esp32s3-elf-g++ - skipped
-- Detecting CXX compile features
-- Detecting CXX compile features - done
-- Building ESP-IDF components for target esp32s3
NOTICE: Processing 5 dependencies:
NOTICE: [1/5] espressif/cmake_utilities (0.5.3)
NOTICE: [2/5] espressif/esp_lcd_gc9a01 (2.0.2)
NOTICE: [3/5] espressif/esp_websocket_client (1.4.0)
NOTICE: [4/5] lvgl/lvgl (8.4.0)
NOTICE: [5/5] idf (5.4.1)
-- Project sdkconfig file /Users/<USER>/code/esp32-mcp-hardware/sdkconfig
Loading defaults file /Users/<USER>/code/esp32-mcp-hardware/sdkconfig.defaults...
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:3 line was updated to # ==================== 分区表配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:7 line was updated to # ==================== 芯片配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:11 line was updated to # ==================== 基础系统配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:25 line was updated to # ==================== WiFi配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:37 line was updated to # ==================== LWIP配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:45 line was updated to # ==================== WebSocket配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:50 line was updated to # ==================== SSL配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:53 line was updated to # ==================== SPI配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:58 line was updated to # ==================== LCD配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:62 line was updated to # ==================== FreeRTOS配置 ====================n
/var/folders/zq/nly47zbs19g601r8k_lqh2n40000gn/T/kconfgen_tmpsr0kur_7:68 line was updated to # ==================== 应用程序特定配置 ====================n
warning: unknown kconfig symbol 'LCD_PANEL_IO_FORMAT_BUF_SIZE' assigned to '32' in /Users/<USER>/code/esp32-mcp-hardware/sdkconfig.defaults
warning: unknown kconfig symbol 'LV_MEM_SIZE' assigned to '32768' in /Users/<USER>/code/esp32-mcp-hardware/sdkconfig.defaults
-- Compiler supported targets: xtensa-esp-elf
-- Found Python3: /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python (found version "3.9.6") found components: Interpreter
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD
-- Performing Test CMAKE_HAVE_LIBC_PTHREAD - Success
-- Found Threads: TRUE
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS
-- Performing Test C_COMPILER_SUPPORTS_WFORMAT_SIGNEDNESS - Success
-- App "esp32_mcp_hardware" version: 1.0.0
-- Adding linker script /Users/<USER>/code/esp32-mcp-hardware/build/esp-idf/esp_system/ld/memory.ld
-- Adding linker script /Users/<USER>/code/esp32-mcp-hardware/build/esp-idf/esp_system/ld/sections.ld.in
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.ld
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.api.ld
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.bt_funcs.ld
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.libgcc.ld
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.wdt.ld
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.version.ld
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/esp32s3/ld/esp32s3.rom.newlib.ld
-- Adding linker script /Users/<USER>/esp/v5.4.1/esp-idf/components/soc/esp32s3/ld/esp32s3.peripherals.ld
-- ESP_LCD_GC9A01: 2.0.2
-- Components: app_trace app_update bootloader bootloader_support bt cmock console cxx driver efuse esp-tls esp_adc esp_app_format esp_bootloader_format esp_coex esp_common esp_driver_ana_cmpr esp_driver_cam esp_driver_dac esp_driver_gpio esp_driver_gptimer esp_driver_i2c esp_driver_i2s esp_driver_isp esp_driver_jpeg esp_driver_ledc esp_driver_mcpwm esp_driver_parlio esp_driver_pcnt esp_driver_ppa esp_driver_rmt esp_driver_sdio esp_driver_sdm esp_driver_sdmmc esp_driver_sdspi esp_driver_spi esp_driver_touch_sens esp_driver_tsens esp_driver_uart esp_driver_usb_serial_jtag esp_eth esp_event esp_gdbstub esp_hid esp_http_client esp_http_server esp_https_ota esp_https_server esp_hw_support esp_lcd esp_local_ctrl esp_mm esp_netif esp_netif_stack esp_partition esp_phy esp_pm esp_psram esp_ringbuf esp_rom esp_security esp_system esp_timer esp_vfs_console esp_wifi espcoredump espressif__cmake_utilities espressif__esp_lcd_gc9a01 espressif__esp_websocket_client esptool_py fatfs freertos hal heap http_parser idf_test ieee802154 json log lvgl__lvgl lwip main mbedtls mqtt newlib nvs_flash nvs_sec_provider openthread partition_table perfmon protobuf-c protocomm pthread rt sdmmc soc spi_flash spiffs tcp_transport touch_element ulp unity usb vfs wear_levelling wifi_provisioning wpa_supplicant xtensa
-- Component paths: /Users/<USER>/esp/v5.4.1/esp-idf/components/app_trace /Users/<USER>/esp/v5.4.1/esp-idf/components/app_update /Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader /Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader_support /Users/<USER>/esp/v5.4.1/esp-idf/components/bt /Users/<USER>/esp/v5.4.1/esp-idf/components/cmock /Users/<USER>/esp/v5.4.1/esp-idf/components/console /Users/<USER>/esp/v5.4.1/esp-idf/components/cxx /Users/<USER>/esp/v5.4.1/esp-idf/components/driver /Users/<USER>/esp/v5.4.1/esp-idf/components/efuse /Users/<USER>/esp/v5.4.1/esp-idf/components/esp-tls /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_adc /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_bootloader_format /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_coex /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ana_cmpr /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_cam /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_dac /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gpio /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_gptimer /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2c /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_i2s /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_isp /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_jpeg /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ledc /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_mcpwm /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_parlio /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_pcnt /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_ppa /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_rmt /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdio /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdm /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdmmc /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_sdspi /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_spi /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_touch_sens /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_tsens /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_uart /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_driver_usb_serial_jtag /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_eth /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_event /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_gdbstub /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hid /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_client /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_http_server /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_ota /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_https_server /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_lcd /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_local_ctrl /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_mm /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_netif_stack /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_partition /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_phy /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_pm /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_psram /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_ringbuf /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_timer /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_vfs_console /Users/<USER>/esp/v5.4.1/esp-idf/components/esp_wifi /Users/<USER>/esp/v5.4.1/esp-idf/components/espcoredump /Users/<USER>/code/esp32-mcp-hardware/managed_components/espressif__cmake_utilities /Users/<USER>/code/esp32-mcp-hardware/managed_components/espressif__esp_lcd_gc9a01 /Users/<USER>/code/esp32-mcp-hardware/managed_components/espressif__esp_websocket_client /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py /Users/<USER>/esp/v5.4.1/esp-idf/components/fatfs /Users/<USER>/esp/v5.4.1/esp-idf/components/freertos /Users/<USER>/esp/v5.4.1/esp-idf/components/hal /Users/<USER>/esp/v5.4.1/esp-idf/components/heap /Users/<USER>/esp/v5.4.1/esp-idf/components/http_parser /Users/<USER>/esp/v5.4.1/esp-idf/components/idf_test /Users/<USER>/esp/v5.4.1/esp-idf/components/ieee802154 /Users/<USER>/esp/v5.4.1/esp-idf/components/json /Users/<USER>/esp/v5.4.1/esp-idf/components/log /Users/<USER>/code/esp32-mcp-hardware/managed_components/lvgl__lvgl /Users/<USER>/esp/v5.4.1/esp-idf/components/lwip /Users/<USER>/code/esp32-mcp-hardware/main /Users/<USER>/esp/v5.4.1/esp-idf/components/mbedtls /Users/<USER>/esp/v5.4.1/esp-idf/components/mqtt /Users/<USER>/esp/v5.4.1/esp-idf/components/newlib /Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_flash /Users/<USER>/esp/v5.4.1/esp-idf/components/nvs_sec_provider /Users/<USER>/esp/v5.4.1/esp-idf/components/openthread /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table /Users/<USER>/esp/v5.4.1/esp-idf/components/perfmon /Users/<USER>/esp/v5.4.1/esp-idf/components/protobuf-c /Users/<USER>/esp/v5.4.1/esp-idf/components/protocomm /Users/<USER>/esp/v5.4.1/esp-idf/components/pthread /Users/<USER>/esp/v5.4.1/esp-idf/components/rt /Users/<USER>/esp/v5.4.1/esp-idf/components/sdmmc /Users/<USER>/esp/v5.4.1/esp-idf/components/soc /Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash /Users/<USER>/esp/v5.4.1/esp-idf/components/spiffs /Users/<USER>/esp/v5.4.1/esp-idf/components/tcp_transport /Users/<USER>/esp/v5.4.1/esp-idf/components/touch_element /Users/<USER>/esp/v5.4.1/esp-idf/components/ulp /Users/<USER>/esp/v5.4.1/esp-idf/components/unity /Users/<USER>/esp/v5.4.1/esp-idf/components/usb /Users/<USER>/esp/v5.4.1/esp-idf/components/vfs /Users/<USER>/esp/v5.4.1/esp-idf/components/wear_levelling /Users/<USER>/esp/v5.4.1/esp-idf/components/wifi_provisioning /Users/<USER>/esp/v5.4.1/esp-idf/components/wpa_supplicant /Users/<USER>/esp/v5.4.1/esp-idf/components/xtensa
-- Configuring done (6.7s)
-- Generating done (0.6s)
-- Build files have been written to: /Users/<USER>/code/esp32-mcp-hardware/build
