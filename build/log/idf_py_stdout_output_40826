[1/5] cd /Users/<USER>/code/esp32-mcp-hardware/build/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 partition --type app /Users/<USER>/code/esp32-mcp-hardware/build/partition_table/partition-table.bin /Users/<USER>/code/esp32-mcp-hardware/build/esp32_mcp_hardware.bin
esp32_mcp_hardware.bin binary size 0x126870 bytes. Smallest app partition is 0x300000 bytes. 0x1d9790 bytes (62%) free.
[2/5] Performing build step for 'bootloader'
[1/1] cd /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/esp-idf/esptool_py && /Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python /Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/check_sizes.py --offset 0x8000 bootloader 0x0 /Users/<USER>/code/esp32-mcp-hardware/build/bootloader/bootloader.bin
Bootloader binary size 0x5470 bytes. 0x2b90 bytes (34%) free.
[3/5] No install step for 'bootloader'
[4/5] Completed 'bootloader'
[4/5] cd /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/esp/v5.4.1/esp-idf -D "SERIAL_TOOL=/Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python;;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3" -D "SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args" -D WORKING_DIRECTORY=/Users/<USER>/code/esp32-mcp-hardware/build -P /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake
esptool.py --chip esp32s3 -p /dev/cu.wchusbserial5A4B0192731 -b 460800 --before=default_reset --after=hard_reset write_flash --flash_mode dio --flash_freq 80m --flash_size 4MB 0x0 bootloader/bootloader.bin 0x10000 esp32_mcp_hardware.bin 0x8000 partition_table/partition-table.bin
esptool.py v4.9.0
Serial port /dev/cu.wchusbserial5A4B0192731

A fatal error occurred: Could not open /dev/cu.wchusbserial5A4B0192731, the port is busy or doesn't exist.
([Errno 35] Could not exclusively lock port /dev/cu.wchusbserial5A4B0192731: [Errno 35] Resource temporarily unavailable)

FAILED: CMakeFiles/flash /Users/<USER>/code/esp32-mcp-hardware/build/CMakeFiles/flash 
cd /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py && /Users/<USER>/.espressif/tools/cmake/3.30.2/CMake.app/Contents/bin/cmake -D IDF_PATH=/Users/<USER>/esp/v5.4.1/esp-idf -D "SERIAL_TOOL=/Users/<USER>/.espressif/python_env/idf5.4_py3.9_env/bin/python;;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/esptool/esptool.py;--chip;esp32s3" -D "SERIAL_TOOL_ARGS=--before=default_reset;--after=hard_reset;write_flash;@flash_args" -D WORKING_DIRECTORY=/Users/<USER>/code/esp32-mcp-hardware/build -P /Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/run_serial_tool.cmake
ninja: build stopped: subcommand failed.
