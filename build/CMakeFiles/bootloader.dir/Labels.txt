# Target labels
 bootloader
# Source files and their labels
/Users/<USER>/code/esp32-mcp-hardware/build/CMakeFiles/bootloader
/Users/<USER>/code/esp32-mcp-hardware/build/CMakeFiles/bootloader.rule
/Users/<USER>/code/esp32-mcp-hardware/build/CMakeFiles/bootloader-complete.rule
/Users/<USER>/code/esp32-mcp-hardware/build/bootloader-prefix/src/bootloader-stamp/bootloader-build.rule
/Users/<USER>/code/esp32-mcp-hardware/build/bootloader-prefix/src/bootloader-stamp/bootloader-configure.rule
/Users/<USER>/code/esp32-mcp-hardware/build/bootloader-prefix/src/bootloader-stamp/bootloader-download.rule
/Users/<USER>/code/esp32-mcp-hardware/build/bootloader-prefix/src/bootloader-stamp/bootloader-install.rule
/Users/<USER>/code/esp32-mcp-hardware/build/bootloader-prefix/src/bootloader-stamp/bootloader-mkdir.rule
/Users/<USER>/code/esp32-mcp-hardware/build/bootloader-prefix/src/bootloader-stamp/bootloader-patch.rule
/Users/<USER>/code/esp32-mcp-hardware/build/bootloader-prefix/src/bootloader-stamp/bootloader-update.rule
