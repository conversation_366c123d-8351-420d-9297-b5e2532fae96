{"COMPONENT_KCONFIGS": "/Users/<USER>/esp/v5.4.1/esp-idf/components/efuse/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_common/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_security/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/log/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/soc/Kconfig;/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/Kconfig", "COMPONENT_KCONFIGS_PROJBUILD": "/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_app_format/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_rom/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/Kconfig.projbuild;/Users/<USER>/esp/v5.4.1/esp-idf/components/partition_table/Kconfig.projbuild", "COMPONENT_SDKCONFIG_RENAMES": "/Users/<USER>/esp/v5.4.1/esp-idf/components/bootloader/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_hw_support/sdkconfig.rename.esp32s3;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/esp_system/sdkconfig.rename.esp32s3;/Users/<USER>/esp/v5.4.1/esp-idf/components/esptool_py/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/freertos/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/hal/sdkconfig.rename;/Users/<USER>/esp/v5.4.1/esp-idf/components/newlib/sdkconfig.rename.esp32s3;/Users/<USER>/esp/v5.4.1/esp-idf/components/spi_flash/sdkconfig.rename", "IDF_TARGET": "esp32s3", "IDF_TOOLCHAIN": "gcc", "IDF_VERSION": "5.4.1", "IDF_ENV_FPGA": "", "IDF_PATH": "/Users/<USER>/esp/v5.4.1/esp-idf", "COMPONENT_KCONFIGS_SOURCE_FILE": "/Users/<USER>/code/esp32-mcp-hardware/build/bootloader/kconfigs.in", "COMPONENT_KCONFIGS_PROJBUILD_SOURCE_FILE": "/Users/<USER>/code/esp32-mcp-hardware/build/bootloader/kconfigs_projbuild.in"}