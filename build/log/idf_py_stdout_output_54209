ESP-ROM:esp32s3-20210327
Build:Mar 27 2021
rst:0x1 (POWERON),boot:0x2b (SPI_FAST_FLASH_BOOT)
SPIWP:0xee
mode:DIO, clock div:1
load:0x3fce2810,len:0x17e8
load:0x403c8700,len:0x4
load:0x403c8704,len:0xd20
load:0x403cb700,len:0x2f00
entry 0x403c8928
[0mI (27) boot: ESP-IDF v5.4.1-dirty 2nd stage bootloader[0m
[0mI (27) boot: compile time Jul 15 2025 17:50:08[0m
[0mI (27) boot: Multicore bootloader[0m
[0mI (30) boot: chip revision: v0.2[0m
[0mI (33) boot: efuse block revision: v1.3[0m
[0mI (37) boot.esp32s3: Boot SPI Speed : 80MHz[0m
[0mI (42) boot.esp32s3: SPI Mode       : DIO[0m
[0mI (46) boot.esp32s3: SPI Flash Size : 4MB[0m
[0mI (51) boot: Enabling RNG early entropy source...[0m
[0mI (56) boot: Partition Table:[0m
[0mI (59) boot: ## Label            Usage          Type ST Offset   Length[0m
[0mI (66) boot:  0 nvs              WiFi data        01 02 00009000 00006000[0m
[0mI (74) boot:  1 phy_init         RF data          01 01 0000f000 00001000[0m
[0mI (81) boot:  2 factory          factory app      00 00 00010000 00300000[0m
[0mI (88) boot: End of partition table[0m
[0mI (92) esp_image: segment 0: paddr=00010020 vaddr=3c0e0020 size=32af0h (207600) map[0m
[0mI (137) esp_image: segment 1: paddr=00042b18 vaddr=3fc9c100 size=04ec8h ( 20168) load[0m
[0mI (141) esp_image: segment 2: paddr=000479e8 vaddr=40374000 size=08630h ( 34352) load[0m
[0mI (150) esp_image: segment 3: paddr=00050020 vaddr=42000020 size=d6ca0h (879776) map[0m
[0mI (305) esp_image: segment 4: paddr=00126cc8 vaddr=4037c630 size=0f9e0h ( 63968) load[0m
[0mI (319) esp_image: segment 5: paddr=001366b0 vaddr=600fe100 size=0001ch (    28) load[0m
[0mI (329) boot: Loaded app from partition at offset 0x10000[0m
[0mI (329) boot: Disabling RNG early entropy source...[0m
[0mI (341) cpu_start: Multicore app[0m
[0mI (350) cpu_start: Pro cpu start user code[0m
[0mI (350) cpu_start: cpu freq: 160000000 Hz[0m
[0mI (350) app_init: Application information:[0m
[0mI (352) app_init: Project name:     esp32_mcp_hardware[0m
[0mI (358) app_init: App version:      1.0.0[0m
[0mI (362) app_init: Compile time:     Jul 15 2025 18:06:47[0m
[0mI (368) app_init: ELF file SHA256:  25f4af0b4...[0m
[0mI (373) app_init: ESP-IDF:          v5.4.1-dirty[0m
[0mI (378) efuse_init: Min chip rev:     v0.0[0m
[0mI (382) efuse_init: Max chip rev:     v0.99 [0m
[0mI (387) efuse_init: Chip rev:         v0.2[0m
[0mI (392) heap_init: Initializing. RAM available for dynamic allocation:[0m
[0mI (399) heap_init: At 3FCB1000 len 00038710 (225 KiB): RAM[0m
[0mI (405) heap_init: At 3FCE9710 len 00005724 (21 KiB): RAM[0m
[0mI (410) heap_init: At 3FCF0000 len 00008000 (32 KiB): DRAM[0m
[0mI (416) heap_init: At 600FE11C len 00001ECC (7 KiB): RTCRAM[0m
[0mI (423) spi_flash: detected chip: boya[0m
[0mI (426) spi_flash: flash io: dio[0m
[0mW (430) spi_flash: Detected size(16384k) larger than the size in the binary image header(4096k). Using the size in the binary image header.[0m
[0mI (444) sleep_gpio: Configure to isolate all GPIO pins in sleep state[0m
[0mI (450) sleep_gpio: Enable automatic switching of GPIO sleep configuration[0m
[0mI (458) main_task: Started on CPU0[0m
[0mI (473) main_task: Calling app_main()[0m
[0mI (474) MAIN: 🚀 小智 ESP32 MCP 硬件系统启动[0m
[0mI (474) MAIN: 📋 系统信息:[0m
[0mI (476) MAIN:    - 芯片型号: esp32s3[0m
[0mI (480) MAIN:    - CPU核心数: 2[0m
[0mI (484) MAIN:    - WiFi/802.11bgn[0m
[0mI (488) MAIN:    - Flash大小: 4MB[0m
[0mI (491) MAIN:    - 剩余内存: 268228 bytes[0m
[0mI (496) MAIN: [0m
[0mI (498) MAIN: 💡 初始化状态LED[0m
[0mI (502) gpio: GPIO[48]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (512) MAIN: 📶 初始化WiFi管理器[0m
[0mI (547) pp: pp rom version: e7ae62f[0m
[0mI (547) net80211: net80211 rom version: e7ae62f[0m
[0;32mI (549) wifi:wifi driver task: 3fcbd420, prio:23, stack:6656, core=0[0m
[0;32mI (566) wifi:wifi firmware version: 79fa3f41ba[0m
[0;32mI (566) wifi:wifi certification version: v7.0[0m
[0;32mI (566) wifi:config NVS flash: enabled[0m
[0;32mI (567) wifi:config nano formatting: disabled[0m
[0;32mI (571) wifi:Init data frame dynamic rx buffer num: 32[0m
[0;32mI (576) wifi:Init static rx mgmt buffer num: 5[0m
[0;32mI (580) wifi:Init management short buffer num: 32[0m
[0;32mI (585) wifi:Init static tx buffer num: 16[0m
[0;32mI (588) wifi:Init static tx FG buffer num: 2[0m
[0;32mI (592) wifi:Init static rx buffer size: 1600[0m
[0;32mI (596) wifi:Init static rx buffer num: 10[0m
[0;32mI (600) wifi:Init dynamic rx buffer num: 32[0m
[0mI (605) wifi_init: rx ba win: 6[0m
[0mI (607) wifi_init: accept mbox: 6[0m
[0mI (611) wifi_init: tcpip mbox: 64[0m
[0mI (615) wifi_init: udp mbox: 32[0m
[0mI (618) wifi_init: tcp mbox: 32[0m
[0mI (622) wifi_init: tcp tx win: 65535[0m
[0mI (626) wifi_init: tcp rx win: 65535[0m
[0mI (630) wifi_init: tcp mss: 1440[0m
[0mI (634) wifi_init: WiFi IRAM OP enabled[0m
[0mI (638) wifi_init: WiFi RX IRAM OP enabled[0m
[0mI (643) WIFI_MANAGER: WiFi manager initialized[0m
[0mI (647) MAIN: 👁️ 初始化双屏眼睛显示系统[0m
[0mI (653) EYE_DISPLAY: 🚀 初始化双屏眼睛显示系统[0m
[0mI (659) EYE_DISPLAY: 🚌 初始化SPI总线[0m
[0mI (664) EYE_DISPLAY: 📚 初始化LVGL库[0m
[0mI (668) EYE_DISPLAY: 💾 为左屏分配显示缓冲区[0m
[0mI (674) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 2)[0m
[0mI (681) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (689) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (698) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (830) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 2)[0m
[0mI (835) EYE_DISPLAY: 💾 为右屏分配显示缓冲区[0m
[0mI (835) EYE_DISPLAY: 🔧 初始化GC9A01屏幕 (CS引脚: 45)[0m
[0mI (839) gpio: GPIO[21]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (847) gpio: GPIO[1]| InputEn: 0| OutputEn: 1| OpenDrain: 0| Pullup: 0| Pulldown: 0| Intr:0 [0m
[0mI (856) gc9a01: LCD panel create success, version: 2.0.2[0m
[0mI (988) EYE_DISPLAY: ✅ GC9A01屏幕初始化完成 (CS引脚: 45)[0m
[0mI (990) EYE_DISPLAY: ⏰ 创建LVGL定时器[0m
[0mI (990) EYE_DISPLAY: ✅ 双屏眼睛显示系统初始化完成[0m
[0mI (995) MAIN: 🔗 连接WiFi: Xiaomi[0m
[0mI (1001) phy_init: phy_version 700,8582a7fd,Feb 10 2025,20:13:11[0m
[0mI (1041) phy_init: Saving new calibration data due to checksum failure or outdated calibration data, mode(0)[0m
[0;32mI (1108) wifi:mode : sta (30:ed:a0:2c:cb:ec)[0m
[0;32mI (1108) wifi:enable tsf[0m
[0mI (1110) WIFI_MANAGER: Connecting to WiFi SSID: Xiaomi[0m
[0;32mI (1123) wifi:new:<7,0>, old:<1,0>, ap:<255,255>, sta:<7,0>, prof:1, snd_ch_cfg:0x0[0m
[0;32mI (1124) wifi:state: init -> auth (0xb0)[0m
[0;32mI (1147) wifi:state: auth -> assoc (0x0)[0m
[0;32mI (1191) wifi:state: assoc -> run (0x10)[0m
[0;32mI (1215) wifi:connected with Xiaomi, aid = 6, channel 7, BW20, bssid = a4:a9:30:b1:c8:be[0m
[0;32mI (1215) wifi:security: WPA2-PSK, phy: bgn, rssi: -48[0m
[0;32mI (1219) wifi:pm start, type: 1[0m

[0;32mI (1219) wifi:dp: 1, bi: 102400, li: 3, scale listen interval from 307200 us to 307200 us[0m
[0;32mI (1228) wifi:set rx beacon pti, rx_bcn_pti: 0, bcn_timeout: 25000, mt_pti: 0, mt_time: 10000[0m
[0;32mI (1246) wifi:<ba-add>idx:0 (ifx:0, a4:a9:30:b1:c8:be), tid:6, ssn:2, winSize:64[0m
[0;32mI (1257) wifi:AP's beacon interval = 102400 us, DTIM period = 1[0m
[0mI (2744) esp_netif_handlers: sta ip: *************, mask: *************, gw: ************[0m
[0mI (2745) WIFI_MANAGER: Got IP:*************[0m
[0mI (2747) MAIN: 📶 WiFi连接成功[0m
[0mI (2751) MAIN: ✅ WiFi连接成功，IP地址: *************[0m
[0mI (2757) MAIN: 📶 信号强度: -48 dBm[0m
[0mI (2761) MAIN: 🔌 初始化MCP客户端[0m
[0mW (2766) websocket_client: `reconnect_timeout_ms` is not set, or it is less than or equal to zero, using default time out 10000 (milliseconds)[0m
[0mW (2779) websocket_client: `network_timeout_ms` is not set, or it is less than or equal to zero, using default time out 10000 (milliseconds)[0m
[0mI (2792) MCP_CLIENT: MCP client initialized with endpoint: wss://api.xiaozhi.me/mcp/?token=***********************************************************************************************************************************************************************************************************************************************************************[0m
[0mI (2824) MAIN: 🛠️ 注册MCP工具...[0m
[0mI (2828) MCP_CLIENT: Tool 'led_control' registered successfully[0m
[0mI (2834) MCP_CLIENT: Tool 'system_info' registered successfully[0m
[0mI (2841) MCP_CLIENT: Tool 'eye_control' registered successfully[0m
[0mI (2847) MCP_CLIENT: Tool 'calculator' registered successfully[0m
[0mI (2853) MAIN: ✅ MCP工具注册完成[0m
[0mI (2860) websocket_client: Started[0m
[0mI (2861) MCP_CLIENT: MCP client task started[0m
[0;32mI (2863) wifi:<ba-add>idx:1 (ifx:0, a4:a9:30:b1:c8:be), tid:0, ssn:0, winSize:64[0m
[0mI (2875) MCP_CLIENT: MCP client started[0m
[0mI (2877) MAIN: 🎬 启动双屏眼睛显示系统[0m
[0mI (2883) EYE_DISPLAY: 🔒 创建LVGL任务[0m
[0mI (2890) EYE_DISPLAY: 🚀 启动LVGL任务[0m
[0mI (2891) EYE_DISPLAY: 🎉 双屏眼睛显示系统启动完成[0m
[0mI (2898) MAIN: 🎉 系统初始化完成！[0m
[0mI (2903) MAIN: 🌟 功能特性：[0m
[0mI (2907) MAIN:    ✨ 双屏模拟眼睛效果 - 动态表情，平滑动画[0m
[0mI (2914) MAIN:    🔗 MCP WebSocket连接 - 支持小智AI远程控制[0m
[0mI (2921) MAIN:    🛠️ 工具注册系统 - 可扩展硬件设备控制[0m
[0mI (2929) MAIN:    💡 LED状态指示 - 连接状态可视化[0m
[0mI (2935) MAIN: [0m
[0mI (2937) MAIN: 💡 LED状态指示说明：[0m
[0mI (2942) MAIN:    - 快速闪烁(100ms): WiFi未连接[0m
[0mI (2947) MAIN:    - 慢速闪烁(500ms): WiFi已连接，MCP未连接[0m
[0mI (2954) MAIN:    - 常亮: WiFi和MCP均已连接[0m
[0mI (2992) EYE_DISPLAY: 👁️ 创建左眼球UI[0m
[0mI (3001) EYE_DISPLAY: ✅ 左眼球UI创建完成[0m
[0mI (3001) EYE_DISPLAY: 👁️ 创建右眼球UI[0m
[0mI (3009) EYE_DISPLAY: ✅ 右眼球UI创建完成[0m
[0mI (3010) EYE_DISPLAY: 🎉 双眼UI创建完成，开始LVGL主循环[0m
[0mI (3012) EYE_DISPLAY: 👁️ 眼睛动画系统已启动[0m
[0mE (3717) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x3F80[0m
[0mE (3718) esp-tls: Failed to open new connection[0m
[0mE (3718) transport_base: Failed to open a new connection[0m
[0mE (3725) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (3731) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_HANDSHAKE_FAILED, tls_error_code=16256, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (3748) MCP_CLIENT: WebSocket error[0m
[0mE (3752) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (3752) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (3758) websocket_client: Reconnect after 10000 ms[0m
[0mI (3769) MCP_CLIENT: WebSocket disconnected[0m
[0mW (3773) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (8764) websocket_client: The client has started[0m
[0mI (8764) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (13764) websocket_client: The client has started[0m
[0mE (15329) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x2880[0m
[0mE (15329) esp-tls: Failed to open new connection[0m
[0mE (15330) transport_base: Failed to open a new connection[0m
[0mE (15338) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (15343) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_HANDSHAKE_FAILED, tls_error_code=10368, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (15360) MCP_CLIENT: WebSocket error[0m
[0mE (15364) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (15364) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (15370) websocket_client: Reconnect after 10000 ms[0m
[0mI (15381) MCP_CLIENT: WebSocket disconnected[0m
[0mW (15386) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (20376) websocket_client: The client has started[0m
[0mI (20376) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (25376) websocket_client: The client has started[0m
[0mE (25540) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x2880[0m
[0mE (25541) esp-tls: Failed to open new connection[0m
[0mE (25541) transport_base: Failed to open a new connection[0m
[0mE (25549) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (25554) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_HANDSHAKE_FAILED, tls_error_code=10368, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (25572) MCP_CLIENT: WebSocket error[0m
[0mE (25576) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (25576) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (25582) websocket_client: Reconnect after 10000 ms[0m
[0mI (25593) MCP_CLIENT: WebSocket disconnected[0m
[0mW (25597) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (30588) websocket_client: The client has started[0m
[0mI (30588) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (35588) websocket_client: The client has started[0m
[0mE (35881) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x2880[0m
[0mE (35881) esp-tls: Failed to open new connection[0m
[0mE (35882) transport_base: Failed to open a new connection[0m
[0mE (35890) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (35895) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_HANDSHAKE_FAILED, tls_error_code=10368, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (35913) MCP_CLIENT: WebSocket error[0m
[0mE (35916) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (35916) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (35922) websocket_client: Reconnect after 10000 ms[0m
[0mI (35933) MCP_CLIENT: WebSocket disconnected[0m
[0mW (35938) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (40928) websocket_client: The client has started[0m
[0mI (40928) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (45928) websocket_client: The client has started[0m
[0mE (46070) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x2880[0m
[0mE (46070) esp-tls: Failed to open new connection[0m
[0mE (46071) transport_base: Failed to open a new connection[0m
[0mE (46079) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (46084) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_HANDSHAKE_FAILED, tls_error_code=10368, tls_flags=0, esp_ws_handshake_status_code=0, errno=119[0m
[0mE (46101) MCP_CLIENT: WebSocket error[0m
[0mE (46105) MAIN: ❌ MCP错误: WebSocket connection error[0m
[0mI (46105) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mI (46111) websocket_client: Reconnect after 10000 ms[0m
[0mI (46122) MCP_CLIENT: WebSocket disconnected[0m
[0mW (46127) MAIN: 🔌 MCP服务器连接断开[0m
[0mE (51117) websocket_client: The client has started[0m
[0mI (51117) MCP_CLIENT: Attempting to reconnect in 5000 ms[0m
[0mE (56117) websocket_client: The client has started[0m
[0mE (56245) esp-tls-mbedtls: mbedtls_ssl_handshake returned -0x2880[0m
[0mE (56246) esp-tls: Failed to open new connection[0m
[0mE (56246) transport_base: Failed to open a new connection[0m
[0mE (56254) transport_ws: Error connecting to host api.xiaozhi.me:443[0m
[0mE (56259) websocket_client: esp_transport_connect() failed with -1, transport_error=ESP_ERR_MBEDTLS_SSL_HANDSHAKE_FAILED, tls_error_code=10368, tls_flags=