/**
 * @file wifi_manager.h
 * @brief WiFi连接管理头文件
 * 
 * ESP32 WiFi连接管理，支持自动重连
 */

#ifndef WIFI_MANAGER_H
#define WIFI_MANAGER_H

#include <string.h>
#include <stdbool.h>
#include "freertos/FreeRTOS.h"
#include "freertos/task.h"
#include "freertos/event_groups.h"
#include "esp_system.h"
#include "esp_wifi.h"
#include "esp_event.h"
#include "esp_log.h"
#include "nvs_flash.h"
#include "esp_netif.h"
#include "app_config.h"

#ifdef __cplusplus
extern "C" {
#endif

// ==================== WiFi状态枚举 ====================

typedef enum {
    WIFI_STATE_DISCONNECTED,
    WIFI_STATE_CONNECTING,
    WIFI_STATE_CONNECTED,
    WIFI_STATE_ERROR
} wifi_state_t;

// ==================== 回调函数类型 ====================

typedef void (*wifi_connection_callback_t)(bool connected);

// ==================== 函数声明 ====================

/**
 * @brief 初始化WiFi管理器
 * @return esp_err_t 初始化结果
 */
esp_err_t wifi_manager_init(void);

/**
 * @brief 启动WiFi连接
 * @param ssid WiFi SSID
 * @param password WiFi密码
 * @return esp_err_t 启动结果
 */
esp_err_t wifi_manager_start(const char* ssid, const char* password);

/**
 * @brief 停止WiFi连接
 */
void wifi_manager_stop(void);

/**
 * @brief 获取WiFi状态
 * @return wifi_state_t 当前状态
 */
wifi_state_t wifi_manager_get_state(void);

/**
 * @brief 检查WiFi是否已连接
 * @return true 已连接
 * @return false 未连接
 */
bool wifi_manager_is_connected(void);

/**
 * @brief 获取IP地址
 * @param ip_str IP地址字符串缓冲区
 * @param len 缓冲区长度
 * @return esp_err_t 获取结果
 */
esp_err_t wifi_manager_get_ip(char* ip_str, size_t len);

/**
 * @brief 获取信号强度
 * @return int8_t RSSI值
 */
int8_t wifi_manager_get_rssi(void);

/**
 * @brief 设置连接状态回调
 * @param callback 回调函数
 */
void wifi_manager_set_callback(wifi_connection_callback_t callback);

/**
 * @brief 等待WiFi连接
 * @param timeout_ms 超时时间(ms)
 * @return true 连接成功
 * @return false 连接失败或超时
 */
bool wifi_manager_wait_connected(uint32_t timeout_ms);

#ifdef __cplusplus
}
#endif

#endif // WIFI_MANAGER_H
